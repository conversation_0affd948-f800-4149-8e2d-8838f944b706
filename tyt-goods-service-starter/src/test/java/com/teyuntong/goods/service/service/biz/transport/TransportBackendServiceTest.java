package com.teyuntong.goods.service.service.biz.transport;

import com.alibaba.fastjson.JSON;
import com.teyuntong.goods.service.TestBase;
import com.teyuntong.goods.service.client.transport.dto.TransportBackendDTO;
import com.teyuntong.goods.service.client.transport.service.TransportBackendRpcService;
import com.teyuntong.goods.service.client.transport.service.TransportMainRpcService;
import com.teyuntong.goods.service.client.transport.vo.TransportBackendVO;
import com.teyuntong.goods.service.client.transport.vo.TransportDynamicVO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.infra.common.rocketmq.core.BatchConsumerBeanInitializer;
import com.teyuntong.infra.common.rocketmq.core.ConsumerBeanInitializer;
import com.teyuntong.infra.common.rocketmq.core.OrderedConsumerBeanInitializer;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

/**
 * 运输管理后台测试类
 *
 * <AUTHOR>
 * @since 2024-01-18 16:08
 */
class TransportBackendServiceTest extends TestBase {

    /**
     * 使用 @MockBean 防止 spring boot 创建 ConsumerBeanInitializer, 导致测试容器类消费消息
     */
    @MockBean
    private ConsumerBeanInitializer consumerBeanInitializer;
    /**
     * 使用 @MockBean 防止 spring boot 创建 orderedConsumerBeanInitializer, 导致测试容器类消费消息
     */
    @MockBean
    private OrderedConsumerBeanInitializer orderedConsumerBeanInitializer;
    /**
     * 使用 @MockBean 防止 spring boot 创建 batchConsumerBeanInitializer, 导致测试容器类消费消息
     */
    @MockBean
    private BatchConsumerBeanInitializer batchConsumerBeanInitializer;

    @Autowired
    private TransportBackendRpcService transportBackendRpcService;

    @Autowired
    private TransportMainRpcService transportMainRpcService;

    @Autowired
    private TransportMainService transportMainService;

    /**
     * 查询货源运输信息（小程序）
     */
    @Test
    void query() {
        TransportBackendDTO transportBackendDTO = new TransportBackendDTO();
        transportBackendDTO.setSrcMsgId(33803263L);
        TransportBackendVO transportBackendVO = transportBackendRpcService.query(transportBackendDTO);
        System.out.println(transportBackendVO);
    }

    @Test
    void modifyConfirmLoadingStatus() {
        transportBackendRpcService.modifyConfirmLoadingStatus(33803263L);
    }

    @Test
    public void tes423() {
        TransportDynamicVO transportDynamic = transportMainService.getTransportDynamic(1002000094L, 0);
        String jsonString = JSON.toJSONString(transportDynamic);
        System.out.println(jsonString);
        System.out.println(1);
    }


}



