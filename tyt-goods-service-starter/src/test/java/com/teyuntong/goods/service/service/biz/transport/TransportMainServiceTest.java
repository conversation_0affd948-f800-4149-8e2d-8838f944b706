package com.teyuntong.goods.service.service.biz.transport;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.goods.service.TestBase;
import com.teyuntong.goods.service.client.callphonerecord.service.CallPhoneRecordRpcService;
import com.teyuntong.goods.service.client.callphonerecord.vo.TransportRecordVo;
import com.teyuntong.goods.service.client.transport.dto.*;
import com.teyuntong.goods.service.client.transport.service.CarAgreementRpcService;
import com.teyuntong.goods.service.client.transport.service.TransportMainRpcService;
import com.teyuntong.goods.service.client.transport.service.TransportRefreshRpcService;
import com.teyuntong.goods.service.client.transport.service.TransportStatusRpcService;
import com.teyuntong.goods.service.client.transport.vo.MyTransportVO;
import com.teyuntong.goods.service.client.transport.vo.TransportMainVO;
import com.teyuntong.goods.service.service.rpc.publish.checker.InvoiceTransportChecker;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 货源测试类
 *
 * <AUTHOR>
 * @since 2024-06-24 15:09
 */
@Slf4j
public class TransportMainServiceTest extends TestBase {

    @Autowired
    private TransportMainRpcService transportMainRpcService;

    @Autowired
    private TransportStatusRpcService transportStatusRpcService;

    @Autowired
    private CallPhoneRecordRpcService callPhoneRecordRpcService;
    @Autowired
    private InvoiceTransportChecker invoiceTransportChecker;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private CarAgreementRpcService carAgreementRpcService;
    @Autowired
    private TransportRefreshRpcService transportRefreshRpcService;

    @Test
    public void test() {
        Object o = redisUtil.hashGet("test", "test2");
        System.out.println(Objects.isNull(o));
    }

    @Test
    public void getMyPublish() {
        TransportListDTO dto = new TransportListDTO();
        dto.setClientSign(21);
        dto.setUserId(1002000094L);
        dto.setQueryActionType(1);
        dto.setQueryMenuType(1);
        MyTransportVO myPublish = transportMainRpcService.getMyPublish(dto);
        log.info("{}", JSONObject.toJSONString(myPublish));
    }

    @Test
    public void getGoodsPoint() {
        GoodsPointDTO dto = new GoodsPointDTO();
        dto.setStartCity("北京市");
        dto.setDestCity("北京市2");
        dto.setDistance(new BigDecimal("202"));
        dto.setWeight(new BigDecimal("23"));
        dto.setUserId(1002000094L);
        dto.setInvoiceTransport(0);
        dto.setSrcMsgId(88823342L);
        GoodsPointResultDTO goodsPoint = transportMainRpcService.getGoodsPoint(dto);
        log.info("result:{}", JSONObject.toJSONString(goodsPoint));
    }

    @Test
    public void queryById() {
//        TransportMainVO transportMainVO = transportMainRpcService.queryById(88823632L);
        List<TransportMainVO> transportMainVOs = transportMainRpcService.queryByIds(Arrays.asList(88823664L, 88823632L, 88823627L, 88823624L));
//        log.info("result:{}", transportMainVO);
        log.info("result:{}", transportMainVOs);
    }

    @Test
    public void test312() {
        List<TransportRecordVo> transportRecordVos = callPhoneRecordRpcService.transportList(1000000485L);
        System.out.println(1);
    }

    @Test
    public void test42342() {
        String ACCOUNT_TWO_TRANSPORT_USER_PRIVACY_PHONE_NUM_COUNT_CACHE_KEY = "accountTwoTransportUserPrivacyPhoneNumCountCacheKey:";

        Date today = new Date();

        String cacheKey = ACCOUNT_TWO_TRANSPORT_USER_PRIVACY_PHONE_NUM_COUNT_CACHE_KEY
                + 1000001L + ":";
        //每个货主每Y（3）天绑定X（4）个后
        int count = 0;
        Integer num1 = redisUtil.getInt(cacheKey + DateUtil.beginOfDay(today).toDateStr());
        if (num1 != null) {
            count += num1;
        }
        Integer num2 = redisUtil.getInt(cacheKey + DateUtil.beginOfDay(DateUtil.offsetDay(today, -1)).toDateStr());
        if (num2 != null) {
            count += num2;
        }
        Integer num3 = redisUtil.getInt(cacheKey + DateUtil.beginOfDay(DateUtil.offsetDay(today, -2)).toDateStr());
        if (num3 != null) {
            count += num3;
        }
        if (count > 4) {
            System.out.println("1");
        }

        Integer num = redisUtil.getInt(cacheKey + DateUtil.beginOfDay(today).toDateStr());
        if (num == null) {
            redisUtil.set(cacheKey + DateUtil.beginOfDay(today).toDateStr(), 1, Duration.ofDays(4));
        } else {
            redisUtil.set(cacheKey + DateUtil.beginOfDay(today).toDateStr(), num + 1, Duration.ofDays(4));
        }

    }

    @Test
    public void testCarAgreementUpdateTransport() {
        String s = "{\"startLongitude\":119.895075,\"startLatitude\":32.394071,\"destLongitude\":118.781047,\"destLatitude\":32.289248,\"srcMsgId\":88823347,\"srcMsgId\":88823347}";
        String s1 = "{\"distance\":\"2000\",\"startPoint\":\"江苏泰州市海陵区\",\"destPoint\":\"江苏南京市六合区\",\"taskContent\":\"高空作业车\",\"startCoord\":\"773.68,3595.55\",\"destCoord\":\"672.28,3582.2\",\"price\":\"1200\",\"startCoordX\":77368,\"startCoordY\":359555,\"destCoordX\":67228,\"destCoordY\":358220,\"startDetailAdd\":\"海陵区 药城大道\",\"startLongitude\":119.895075,\"startLatitude\":32.394071,\"destDetailAdd\":\"六合区 方新路\",\"destLongitude\":118.781047,\"destLatitude\":32.289248,\"weight\":\"20.0\",\"length\":\"13.0\",\"wide\":\"2.5\",\"high\":\"2.5\",\"startCity\":\"泰州市\",\"srcMsgId\":88823347,\"startProvinc\":\"江苏\",\"startArea\":\"海陵区\",\"destProvinc\":\"江苏\",\"destCity\":\"南京市\",\"destArea\":\"六合区\",\"refundFlag\":1,\"additionalPrice\":\"67\",\"enterpriseTaxRate\":5.30}";
        TransportMainDTO bean = JSONUtil.toBean(s1, TransportMainDTO.class);

        carAgreementRpcService.carAgreementUpdateTransport(bean);
    }


    @Test
    public void test424() {
        TransportDoneDTO transportDoneDTO = new TransportDoneDTO();
//        transportStatusRpcService.setGoodsBackOut(new SaveGoodsStatusDTO(10001L, 88823402L, "重新发布", 3, transportDoneDTO));
    }

    @Test
    public void testSegmentedPayments() {

        invoiceTransportChecker.checkSegmentedPayments(BigDecimal.valueOf(60)
                , BigDecimal.valueOf(100),
                BigDecimal.valueOf(40)
                , "200", 1002000054L, false, true, null);
    }

    @Test
    public void testMinRefreshInterval() {
        Integer integer = transportRefreshRpcService.minRefreshInterval(1002000780L);
        System.out.println(integer);

    }

    @Test
    public void getTransportForUser() {
        List<Long> transportForUser = transportMainRpcService.getTransportForUser(1002000780L, null);
        System.out.println(JSONUtil.toJsonStr(transportForUser));

    }


}
