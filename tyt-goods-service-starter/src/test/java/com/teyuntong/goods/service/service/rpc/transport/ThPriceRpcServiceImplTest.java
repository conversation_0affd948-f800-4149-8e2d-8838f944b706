package com.teyuntong.goods.service.service.rpc.transport;

import com.teyuntong.goods.service.client.transport.vo.ChangePriceLogReq;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.ChangePriceLogDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.ChangePriceLogMapper;
import com.teyuntong.goods.service.service.biz.transport.service.ThPriceService;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.Date;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * ThPriceRpcServiceImpl 单元测试类
 *
 * <AUTHOR>
 * @since 2024-05-15
 */
@ExtendWith(MockitoExtension.class)
class ThPriceRpcServiceImplTest {

    @Mock
    private ThPriceService thPriceService;

    @Mock
    private RedisUtil redisUtil;

    @Mock
    private ChangePriceLogMapper changePriceLogMapper;

    @Mock
    private StringRedisTemplate stringRedisTemplate;

    @Mock
    private ValueOperations<String, String> valueOperations;

    @InjectMocks
    private ThPriceRpcServiceImpl thPriceRpcService;

    private ChangePriceLogReq validRequest;

    @BeforeEach
    void setUp() {
        // 准备一个有效的请求对象
        validRequest = new ChangePriceLogReq();
        validRequest.setSrcMsgId(86766661L);
        validRequest.setPrice(new BigDecimal("1000.00"));
        validRequest.setPublishType(2); // 一口价
        validRequest.setOperationType(1); // 编辑发布

        // 设置StringRedisTemplate的mock行为
        when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);
    }

    @Test
    void changePirceLog_WithNullParameters_ShouldReturnEarly() {
        // 准备测试数据 - 各种参数为空的情况
        ChangePriceLogReq nullSrcMsgIdReq = new ChangePriceLogReq();
        nullSrcMsgIdReq.setPrice(new BigDecimal("1000.00"));
        nullSrcMsgIdReq.setPublishType(2);
        nullSrcMsgIdReq.setOperationType(1);

        ChangePriceLogReq nullPriceReq = new ChangePriceLogReq();
        nullPriceReq.setSrcMsgId(123456L);
        nullPriceReq.setPublishType(2);
        nullPriceReq.setOperationType(1);

        ChangePriceLogReq nullPublishTypeReq = new ChangePriceLogReq();
        nullPublishTypeReq.setSrcMsgId(123456L);
        nullPublishTypeReq.setPrice(new BigDecimal("1000.00"));
        nullPublishTypeReq.setOperationType(1);

        ChangePriceLogReq nullOperationTypeReq = new ChangePriceLogReq();
        nullOperationTypeReq.setSrcMsgId(123456L);
        nullOperationTypeReq.setPrice(new BigDecimal("1000.00"));
        nullOperationTypeReq.setPublishType(2);

        // 执行测试
        thPriceRpcService.changePirceLog(nullSrcMsgIdReq);
        thPriceRpcService.changePirceLog(nullPriceReq);
        thPriceRpcService.changePirceLog(nullPublishTypeReq);
        thPriceRpcService.changePirceLog(nullOperationTypeReq);

        // 验证 - 所有参数为空的情况下，不应该调用 Redis 或数据库操作
        verify(valueOperations, never()).get(anyString());
        verify(valueOperations, never()).setIfAbsent(anyString(), anyString(), any(Duration.class));
        verify(changePriceLogMapper, never()).getLatestLogBySrcMsgId(anyLong());
        verify(changePriceLogMapper, never()).insert(any(ChangePriceLogDO.class));
    }

    @Test
    void changePirceLog_WithNoEarliestPrice_ShouldSaveToRedis() {
        // 模拟 Redis 返回 null，表示没有初始价格
        when(valueOperations.get(anyString())).thenReturn(null);

        // 执行测试
        thPriceRpcService.changePirceLog(validRequest);

        // 验证 - 应该调用 Redis 保存初始价格
        verify(valueOperations).get("EarliestPrice:" + validRequest.getSrcMsgId());
        verify(valueOperations).setIfAbsent(
                eq("EarliestPrice:" + validRequest.getSrcMsgId()),
                eq(validRequest.getPrice().toString()),
                any(Duration.class));
    }

    @Test
    void changePirceLog_WithExistingEarliestPrice_ShouldNotSaveToRedis() {
        // 模拟 Redis 返回已存在的初始价格
        when(valueOperations.get(anyString())).thenReturn("1000");

        // 执行测试
        thPriceRpcService.changePirceLog(validRequest);

        // 验证 - 不应该调用 Redis 保存初始价格
        verify(valueOperations).get("EarliestPrice:" + validRequest.getSrcMsgId());
        verify(valueOperations, never()).setIfAbsent(anyString(), anyString(), any(Duration.class));
    }

    @Test
    void changePirceLog_WithNoLatestLog_ShouldInsertNewLog() {
        // 模拟 Redis 返回已存在的初始价格
        when(valueOperations.get(anyString())).thenReturn("1000");

        // 模拟数据库没有最近的日志记录
        when(changePriceLogMapper.getLatestLogBySrcMsgId(anyLong())).thenReturn(null);

        // 执行测试
        thPriceRpcService.changePirceLog(validRequest);

        // 验证 - 应该插入新的日志记录
        verify(changePriceLogMapper).getLatestLogBySrcMsgId(validRequest.getSrcMsgId());
        verify(changePriceLogMapper).insert(any(ChangePriceLogDO.class));
    }

    @Test
    void changePirceLog_WithDifferentPrice_ShouldInsertNewLog() {
        // 模拟 Redis 返回已存在的初始价格
        when(valueOperations.get(anyString())).thenReturn("1000");

        // 模拟数据库有最近的日志记录，但价格不同
        ChangePriceLogDO latestLog = new ChangePriceLogDO();
        latestLog.setId(1L);
        latestLog.setSrcMsgId(validRequest.getSrcMsgId());
        latestLog.setPrice(new BigDecimal("900.00")); // 不同的价格
        latestLog.setPublishType(validRequest.getPublishType());
        latestLog.setOperationType(validRequest.getOperationType());
        latestLog.setCreateTime(new Date());

        when(changePriceLogMapper.getLatestLogBySrcMsgId(anyLong())).thenReturn(latestLog);

        // 执行测试
        thPriceRpcService.changePirceLog(validRequest);

        // 验证 - 应该插入新的日志记录
        verify(changePriceLogMapper).getLatestLogBySrcMsgId(validRequest.getSrcMsgId());
        verify(changePriceLogMapper).insert(any(ChangePriceLogDO.class));
    }

    @Test
    void changePirceLog_WithDifferentPublishType_ShouldInsertNewLog() {
        // 模拟 Redis 返回已存在的初始价格
        when(valueOperations.get(anyString())).thenReturn("1000");

        // 模拟数据库有最近的日志记录，但发布类型不同
        ChangePriceLogDO latestLog = new ChangePriceLogDO();
        latestLog.setId(1L);
        latestLog.setSrcMsgId(validRequest.getSrcMsgId());
        latestLog.setPrice(validRequest.getPrice());
        latestLog.setPublishType(1); // 不同的发布类型
        latestLog.setOperationType(validRequest.getOperationType());
        latestLog.setCreateTime(new Date());

        when(changePriceLogMapper.getLatestLogBySrcMsgId(anyLong())).thenReturn(latestLog);

        // 执行测试
        thPriceRpcService.changePirceLog(validRequest);

        // 验证 - 应该插入新的日志记录
        verify(changePriceLogMapper).getLatestLogBySrcMsgId(validRequest.getSrcMsgId());
        verify(changePriceLogMapper).insert(any(ChangePriceLogDO.class));
    }

    @Test
    void changePirceLog_WithSamePriceAndPublishType_ShouldNotInsertNewLog() {
        // 模拟 Redis 返回已存在的初始价格
        when(valueOperations.get(anyString())).thenReturn("1000");

        // 模拟数据库有最近的日志记录，且价格和发布类型相同
        ChangePriceLogDO latestLog = new ChangePriceLogDO();
        latestLog.setId(1L);
        latestLog.setSrcMsgId(validRequest.getSrcMsgId());
        latestLog.setPrice(validRequest.getPrice());
        latestLog.setPublishType(validRequest.getPublishType());
        latestLog.setOperationType(validRequest.getOperationType());
        latestLog.setCreateTime(new Date());

        when(changePriceLogMapper.getLatestLogBySrcMsgId(anyLong())).thenReturn(latestLog);

        // 执行测试
        thPriceRpcService.changePirceLog(validRequest);

        // 验证 - 不应该插入新的日志记录
        verify(changePriceLogMapper).getLatestLogBySrcMsgId(validRequest.getSrcMsgId());
        verify(changePriceLogMapper, never()).insert(any(ChangePriceLogDO.class));
    }

    @Test
    void changePirceLog_WithZeroPrice_ShouldNotSaveToRedis() {
        // 准备测试数据 - 价格为零
        ChangePriceLogReq zeroPriceReq = new ChangePriceLogReq();
        zeroPriceReq.setSrcMsgId(123456L);
        zeroPriceReq.setPrice(BigDecimal.ZERO);
        zeroPriceReq.setPublishType(2);
        zeroPriceReq.setOperationType(1);

        // 模拟 Redis 返回 null，表示没有初始价格
        when(valueOperations.get(anyString())).thenReturn(null);

        // 执行测试
        thPriceRpcService.changePirceLog(zeroPriceReq);

        // 验证 - 不应该调用 Redis 保存初始价格
        verify(valueOperations).get("EarliestPrice:" + zeroPriceReq.getSrcMsgId());
        verify(valueOperations, never()).setIfAbsent(anyString(), anyString(), any(Duration.class));
    }
}
