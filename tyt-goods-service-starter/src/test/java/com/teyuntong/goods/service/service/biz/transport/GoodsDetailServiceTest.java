package com.teyuntong.goods.service.service.biz.transport;

import com.teyuntong.goods.service.TestBase;
import com.teyuntong.goods.service.client.goodsrecord.service.ExposureCardGiveawayRpcService;
import com.teyuntong.goods.service.client.transport.dto.ShowAddPriceDTO;
import com.teyuntong.goods.service.client.transport.service.GoodsDetailRpcService;
import com.teyuntong.goods.service.service.biz.exposure.service.ExposureCardGiveawayService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

/**
 * 货源详情
 *
 * <AUTHOR>
 * @since 2025-04-02 18:11
 */
@Slf4j
public class GoodsDetailServiceTest extends TestBase {
    @Resource
    private GoodsDetailRpcService goodsDetailRpcService;

    @Autowired
    private TransportMainService transportMainService;

    @Autowired
    private ExposureCardGiveawayService exposureCardGiveawayService;

    @Autowired
    private ExposureCardGiveawayRpcService exposureCardGiveawayRpcService;

    @Test
    void showAddPric2e() {
        exposureCardGiveawayRpcService.updateStatus();
    }

    @Test
    void showAddPrice() {
        TransportMainDO transportMainDO = transportMainService.getById(88823722L);
        exposureCardGiveawayService.checkAndSave(transportMainDO);
    }
}
