package com.teyuntong.goods.service.service.biz.order;

import com.alibaba.fastjson.JSON;
import com.teyuntong.goods.service.TestBase;
import com.teyuntong.goods.service.service.biz.order.dto.SameTransportAvgPriceResultDTO;
import com.teyuntong.goods.service.service.biz.order.service.TransportAfterOrderDataService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

/**
 * 运输管理后台测试类
 *
 * <AUTHOR>
 * @since 2024-01-18 16:08
 */
@Slf4j
class TransportAfterOrderDataServiceTest extends TestBase {

    @Autowired
    private TransportAfterOrderDataService transportAfterOrderDataService;

    @Autowired
    private TransportMainService transportMainService;

    @Test
    void getSameTransportAvgPrice() {
        SameTransportAvgPriceResultDTO sameTransportAvgPrice =
                transportAfterOrderDataService.getSameTransportAvgPrice(88823594L, null);
        System.out.println(JSON.toJSONString(sameTransportAvgPrice));
    }

    @Test
    void getSameTransportAvgPrice2() {
        BigDecimal sameTransportAvgPrice = transportAfterOrderDataService.getSameTransportAvgPriceByDays(88823594L, 30);
        log.info("{}", sameTransportAvgPrice);
    }

}



