spring:
  #上传文件大小设置
  servlet:
    multipart:
      enabled: true
      #上传单个文件大小限制
      max-file-size: 10MB
      #上传总文件大小限制
      max-request-size: 50MB
  datasource:
    dynamic:
      primary: tyt
      datasource:
        tyt:
          url: '***********************************************************************************************************************************************'
          username: tyt_dev
          password: tyt_dev#20200724
#          url: '**********************************************************************************************************************************************************************************'
#          username: test_readonly
#          password: test_readonly@TE
          driverClassName: com.mysql.cj.jdbc.Driver
        recommend:
          url: '*********************************************************************************************************************'
          username: tyt_dev
          password: tyt_dev#20200724
#          url: '********************************************************************************************************************************************************************************************'
#          username: test_readonly
#          password: test_readonly@TE
          driver-class-name: com.mysql.cj.jdbc.Driver
        tytLog:
          url: '**************************************************************************************************************'
          username: tyt_dev
          password: tyt_dev#20200724
          driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        maximum-pool-size: 40
        minimum-idle: 15
        max-lifetime: 30000
  redis:
    database: 0
    host: public-network-dev-0.redis.rds.aliyuncs.com
    port: 16379
    password: TyT@dev#20220323
#    database: 2
#    host: r-2zehe8qbmlx8071kcp.redis.rds.aliyuncs.com
#    port: 6379
#    password: tyt_Test_redis0608
    lettuce:
      pool:
        # 最大连接数
        max-active: 8
        # 最大空闲连接数
        max-idle: 8
        # 最小空闲连接数
        min-idle: 2
custom:
  feign:
    decoder:
      log-business-exception: true
      throw-business-exception: true
mybatis-plus:
  configuration:
    auto-mapping-behavior: full
  mapper-locations: classpath:mapper/*.xml
retrofit:
  circuitbreaker:
    resilience4j:
      enable: true
resilience4j:
  circuitbreaker:
    configs:
      default:
        minimumNumberOfCalls: 10
        slidingWindowSize: 60
        failureRateThreshold: 50
  timelimiter:
    configs:
      default:
        timeoutDuration: 3s
xxl-job:
  enable: false
rocket-mq:
  consumer:
    enable: true
  producer:
    enable: false
  nameSrvAddr: http://MQ_INST_1955986389231769_BX2m3KBq.cn-beijing.mq-internal.aliyuncs.com:8080 # ns 地址
  accessKey: LTAI5t9324vzFd6VLMybLzoE
  secretKey: ******************************
  consumer-groups: # 消费者组, 没组对应的 key 为阿里云后台创建的消费者组, 框架会针对每个消费者组创建一个消费者实例
    GID_DEVELOPMENT_MQ:
      consumeThreadNums: 10 # 消费线程数
      topics: # 订阅的 topic 信息, 每个消费着组需要保持订阅关系一致, 详见 https://help.aliyun.com/zh/apsaramq-for-rocketmq/cloud-message-queue-rocketmq-4-x-series/use-cases/subscription-consistency
        - topic: MESSAGE_CENTER_TOPIC_LOCAL
          expression: gray
          type: tag
    GID_GOODS_CENTER_TOPIC_DISPATCH:
      consumeThreadNums: 10 # 消费线程数
      topics: # 订阅的 topic 信息, 每个消费着组需要保持订阅关系一致, 详见 https://help.aliyun.com/zh/apsaramq-for-rocketmq/cloud-message-queue-rocketmq-4-x-series/use-cases/subscription-consistency
        - topic: GOODS_CENTER_TOPIC
          expression: DISPATCH
          type: TAG
    GID_GOODS_CENTER_TOPIC_TRANSPORT_PUBLISH:
      consumeThreadNums: 10 # 消费线程数
      topics: # 订阅的 topic 信息, 每个消费着组需要保持订阅关系一致, 详见 https://help.aliyun.com/zh/apsaramq-for-rocketmq/cloud-message-queue-rocketmq-4-x-series/use-cases/subscription-consistency
        - topic: GOODS_CENTER_TOPIC
          expression: TRANSPORT_PUBLISH
          type: TAG

message-center:
  mq-topic: MESSAGE_CENTER_TOPIC_DEV
  mq-tag: tag
  suffix: _goodsService
  callback-url: http://************:8040/message/center/saveMqDeliverFail

mq-topic:
  topic: TYT_DEVELOPMENT_MQ
  tag: tag
  mbTopic: DEV_SYNC_MB_GOODS_SOURCE
  mbTag: tag
  tradeCenterTopic: TRADE_CENTER_TOPIC
  tradeOrderTag: trade_normal
  tradePayTag: trade_pay

easy-es:
  address: es-cn-jte3nts0v000uamhq.public.elasticsearch.aliyuncs.com:9200 # es的连接地址,必须含端口 若为集群,则可以用逗号隔开 例如:127.0.0.1:9200,*********:9200
  username: elastic #如果无账号密码则可不配置此行
  password: Es#Tyt@20210524 #如果无账号密码则可不配置此行
  keep-alive-millis: 300000 # 心跳策略时间 单位:ms
  connect-timeout: 10000 # 连接超时时间 单位:ms
  socket-timeout: 20000 # 通信超时时间 单位:ms
  connection-request-timeout: 5000 # 连接请求超时时间 单位:ms
  max-conn-total: 1024 # 最大连接数 单位:个
  max-conn-per-route: 500 # 最大连接路由数 单位:个
  global-config:
    db-config:
      index-prefix: dev_
      map-underscore-to-camel-case: true
logging:
  level:
    root: info
    com.teyuntong: debug