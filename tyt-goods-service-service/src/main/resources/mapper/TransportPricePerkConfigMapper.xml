<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.publish.mybatis.mapper.TransportPricePerkConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.service.service.biz.publish.mybatis.entity.TransportPricePerkConfigDO">
        <id column="id" property="id" />
        <result column="start_city" property="startCity" />
        <result column="dest_city" property="destCity" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="perk_ratio" property="perkRatio" />
        <result column="distance_min" property="distanceMin" />
        <result column="distance_max" property="distanceMax" />
        <result column="status" property="status" />
        <result column="delete_status" property="deleteStatus" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="creator" property="creator" />
        <result column="operator" property="operator" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, start_city, dest_city, start_time, end_time, perk_ratio, distance_min, distance_max, status, delete_status, create_time, modify_time, creator, operator
    </sql>

    <select id="getConfigByCity" resultMap="BaseResultMap">
        select * from tyt_transport_price_perk_config where start_city = #{startCity} and dest_city = #{destCity} and status = 1 and delete_status = 0 and start_time &lt;=NOW() and end_time>=NOW() order by id limit 1
    </select>

</mapper>
