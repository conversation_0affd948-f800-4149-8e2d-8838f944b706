<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.cover.mybatis.mapper.CoverGoodsBeansConfigUserMapper">

    <select id="selectTotalBeansLeftNumByUserId"
            resultType="com.teyuntong.goods.service.service.biz.cover.dto.CoverGoodsUserBeansDTO">
        select user_id as userId, sum(left_num) as totalLeftNum
        from tyt_cover_goods_beans_config_user
        where user_id = #{userId}
          and beans_status = 1
    </select>

</mapper>
