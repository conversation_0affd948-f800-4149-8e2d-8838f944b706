<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.cover.mybatis.mapper.CoverGoodsLogMapper">

    <select id="countTodayCoverTimesWithoutGoodGoods" resultType="java.lang.Integer">
        select count(*) from tyt_cover_goods_log
        where user_id = #{userId}
          and create_time > curdate()
          and goods_level != 1
    </select>
</mapper>
