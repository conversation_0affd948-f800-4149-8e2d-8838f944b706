<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TytFreeTecServiceFeeCarUserLogMapper">
    <insert id="replaceIntoByCarUserIdAndSrcMsgId">
        replace INTO tyt_free_tec_service_fee_car_user_log (
            src_msg_id,
            car_user_id,
            view_time,
            create_time
        ) VALUES (
                     #{srcMsgId},
                     #{carUserId},
                     now(),
                     now()
                 )
    </insert>
</mapper>