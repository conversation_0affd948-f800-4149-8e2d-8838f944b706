<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.invoice.mybatis.mapper.InvoiceTransportPriceEnterpriseConfigMapper">

    <select id="getPriceConfigByEnterpriseId" resultType="com.teyuntong.goods.service.service.biz.invoice.mybatis.entity.InvoiceTransportPriceEnterpriseConfigDO">
        select *
        from tyt_invoice_transport_price_enterprise_config
        where enterprise_id = #{enterpriseId}
    </select>
</mapper>
