<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportExtendMapper">
    <update id="updateTransportExtend">
        UPDATE tyt_transport_extend
        SET src_msg_id           = #{transportExtend.srcMsgId},
            ts_id                = #{transportExtend.tsId},
            use_car_type         = #{transportExtend.useCarType},
            price_type           = #{transportExtend.priceType},
            suggest_min_price    = #{transportExtend.suggestMinPrice},
            suggest_max_price    = #{transportExtend.suggestMaxPrice},
            create_time          = #{transportExtend.createTime},
            modify_time          = #{transportExtend.modifyTime},
            good_model_level     = #{transportExtend.goodModelLevel},
            good_model_score     = #{transportExtend.goodModelScore},
            price_cap            = #{transportExtend.priceCap},
            lim_good_model_level = #{transportExtend.limGoodModelLevel},
            lim_good_model_score = #{transportExtend.limGoodModelScore},
            commission_score     = #{transportExtend.commissionScore},
            top_flag             = #{transportExtend.topFlag},
            seckill_goods        = #{transportExtend.seckillGoods},
            perk_price           = #{transportExtend.perkPrice},
            good_transport_label = #{transportExtend.goodTransportLabel}
        WHERE id = #{transportExtend.id}
    </update>

    <select id="getByTsId"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportExtendDO">
        select *
        from tyt_transport_extend
        where ts_id = #{tsId}
    </select>
</mapper>