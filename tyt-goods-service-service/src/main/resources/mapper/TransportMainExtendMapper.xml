<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportMainExtendMapper">

    <update id="updateMainExtend">
        UPDATE tyt_transport_main_extend
        SET src_msg_id           = #{mainExtend.srcMsgId},
            use_car_type         = #{mainExtend.useCarType},
            price_type           = #{mainExtend.priceType},
            suggest_min_price    = #{mainExtend.suggestMinPrice},
            suggest_max_price    = #{mainExtend.suggestMaxPrice},
            create_time          = #{mainExtend.createTime},
            modify_time          = #{mainExtend.modifyTime},
            good_model_level     = #{mainExtend.goodModelLevel},
            good_model_score     = #{mainExtend.goodModelScore},
            price_cap            = #{mainExtend.priceCap},
            lim_good_model_level = #{mainExtend.limGoodModelLevel},
            lim_good_model_score = #{mainExtend.limGoodModelScore},
            commission_score     = #{mainExtend.commissionScore},
            seckill_goods        = #{mainExtend.seckillGoods},
            perk_price           = #{mainExtend.perkPrice},
            good_transport_label = #{mainExtend.goodTransportLabel}
        WHERE id = #{mainExtend.id};
    </update>

    <select id="getBySrcMsgId" resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO">
        select * from tyt_transport_main_extend where src_msg_id = #{srcMsgId}
    </select>

    <select id="getBySrcMsgIds" resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO">
        select *
        from tyt_transport_main_extend
        where src_msg_id in
        <foreach collection="srcMsgIds" item="srcMsgId" open="(" separator="," close=")">
            #{srcMsgId}
        </foreach>
    </select>

	<select id="getPriceTypeBySrcMsgId" resultType="java.lang.Integer">
        select price_type
        from tyt_transport_main_extend where src_msg_id = #{srcMsgId}
    </select>
</mapper>