<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.goodsname.mybatis.mapper.KeywordMatchesNewUnstandardMapper">


    <select id="selectByKeywords"
            resultType="com.teyuntong.goods.service.service.biz.goodsname.mybatis.entity.KeywordMatchesNewUnstandardDO">
        select * from tyt_keyword_matches_new_unstandard
        where key_words in
        <foreach collection="keywords" item="keyword" open="(" separator="," close=")">
            #{keyword}
        </foreach>
    </select>
</mapper>
