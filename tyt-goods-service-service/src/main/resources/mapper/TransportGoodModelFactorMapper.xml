<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportGoodModelFactorMapper">

    <select id="matchRules"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportGoodModelFactorDO">
        SELECT *
        FROM tyt_transport_good_model_factor
        WHERE #{weight} >= weight_start
        AND weight_end > #{weight}
        AND #{distance} >= distance_start
        AND distance_end > #{distance}
    </select>
</mapper>
