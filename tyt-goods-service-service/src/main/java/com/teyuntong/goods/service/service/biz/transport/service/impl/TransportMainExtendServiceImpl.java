package com.teyuntong.goods.service.service.biz.transport.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportMainExtendMapper;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainExtendService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/22 14:36
 */
@Service
@RequiredArgsConstructor
public class TransportMainExtendServiceImpl extends ServiceImpl<TransportMainExtendMapper, TransportMainExtendDO> implements TransportMainExtendService {

    private final TransportMainExtendMapper transportMainExtendMapper;

    @Override
    public TransportMainExtendDO getBySrcMsgId(Long srcMsgId) {
        return transportMainExtendMapper.getBySrcMsgId(srcMsgId);
    }

    @Override
    public int updateMainExtendById(TransportMainExtendDO mainExtend) {
        return transportMainExtendMapper.updateById(mainExtend);
    }

    @Override
    public List<TransportMainExtendDO> getBySrcMsgIds(List<Long> srcMsgIds) {
        return transportMainExtendMapper.getBySrcMsgIds(srcMsgIds);
    }

    @Override
    public int updateMainExtend(TransportMainExtendDO mainExtend) {
        return transportMainExtendMapper.updateMainExtend(mainExtend);
    }
}
