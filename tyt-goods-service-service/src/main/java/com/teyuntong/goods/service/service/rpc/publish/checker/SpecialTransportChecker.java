package com.teyuntong.goods.service.service.rpc.publish.checker;

import cn.hutool.core.collection.CollUtil;
import com.teyuntong.goods.service.client.publish.dto.CalcSpecialGoodsPriceResultDTO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchCompanyDO;
import com.teyuntong.goods.service.service.biz.transport.service.DispatchCompanyService;
import com.teyuntong.goods.service.service.common.enums.ExcellentGoodsEnums;
import com.teyuntong.goods.service.service.common.enums.PriceTypeEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.builder.CalcSpecialGoodsPriceService;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 运满满货源校验
 *
 * <AUTHOR>
 * @since 2025/02/21 13:28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpecialTransportChecker {

    private final DispatchCompanyService dispatchCompanyService;
    private final CalcSpecialGoodsPriceService calcSpecialGoodsPriceService;

    /**
     * 校验专车运费并返回运价类型
     *
     * @param publishProcessBO
     * @param user
     * @return
     */
    public void specialCheck(PublishProcessBO publishProcessBO, UserRpcVO user) {
        PublishBO publishBO = publishProcessBO.getPublishBO();
        CalcSpecialGoodsPriceResultDTO specialGoodsPriceResult = publishProcessBO.getSpecialGoodsPriceResult();
        // 专车发货如果是代调用户，签约合作商为必选项
        if (Objects.equals(publishBO.getExcellentGoods(), ExcellentGoodsEnums.SPECIAL.getCode())) {

            List<DispatchCompanyDO> companyDOList = dispatchCompanyService.selectByUserId(user.getId());
            if (CollUtil.isNotEmpty(companyDOList) && (Objects.isNull(publishBO.getCargoOwnerId()) || publishBO.getCargoOwnerId() == 0)) {
                throw new BusinessException(GoodsErrorCode.NO_SIGN_SUBJECT);
            }
            if (specialGoodsPriceResult != null && Objects.equals(PriceTypeEnum.FLEXIBLE.getCode(), specialGoodsPriceResult.getPriceType()) && (null == specialGoodsPriceResult.getPerkPrice() || specialGoodsPriceResult.getPerkPrice().compareTo(BigDecimal.ZERO) <= 0)) {
                BigDecimal lowerLimitPrice = specialGoodsPriceResult.getLowerLimitPrice();
                BigDecimal userSubmitPrice = new BigDecimal(publishBO.getPrice());
                if (userSubmitPrice.compareTo(lowerLimitPrice) < 0) {
                    throw BusinessException.createException(GoodsErrorCode.PRICE_TOO_LOW_OR_HIGH.getCode(), "为了不影响接单，最低建议运费为" + lowerLimitPrice + "元");
                }
            }
            if (specialGoodsPriceResult != null){
                BigDecimal perkPrice = specialGoodsPriceResult.getPerkPrice() == null ? BigDecimal.ZERO : specialGoodsPriceResult.getPerkPrice();
                // 获取数据库中原有的 perkPrice（int 类型）
                BigDecimal publishPerkPrice = publishBO.getPerkPrice();
                // 判断当前计算出的 perkPrice 是否存在且大于0
                boolean hasCurrentPerk = perkPrice.compareTo(BigDecimal.ZERO) > 0;
                // 判断数据库中的 perkPrice 是否存在且大于0
                boolean hasDbPerk = publishPerkPrice!=null && publishPerkPrice.compareTo(BigDecimal.ZERO) > 0;

                if (hasCurrentPerk) {
                    // 如果当前有优惠，但数据库中没有 或者 金额不一致 → 优惠比例变化
                    if (!hasDbPerk || publishPerkPrice.compareTo(perkPrice) != 0) {
                        throw new BusinessException(GoodsErrorCode.PUBLISH_PERK_RATIO_CHANGE);
                    }
                } else {
                    // 当前无优惠，但数据库中有 → 活动已结束
                    if (hasDbPerk) {
                        throw new BusinessException(GoodsErrorCode.PUBLISH_PERK_ACTIVITY_END);
                    }
                }
            }
        }

    }


}
