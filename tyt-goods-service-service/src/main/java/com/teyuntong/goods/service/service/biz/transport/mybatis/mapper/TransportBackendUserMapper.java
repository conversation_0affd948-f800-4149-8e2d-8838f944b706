package com.teyuntong.goods.service.service.biz.transport.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportBackendUserDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-02-22
 */
@Mapper
public interface TransportBackendUserMapper extends BaseMapper<TransportBackendUserDO> {

    void updateByBackendIdAndBatch(@Param("id") Long id, @Param("batch") String batch);
}
