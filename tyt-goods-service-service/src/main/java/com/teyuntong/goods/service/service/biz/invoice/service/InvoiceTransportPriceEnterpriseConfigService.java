package com.teyuntong.goods.service.service.biz.invoice.service;


import com.teyuntong.goods.service.service.biz.invoice.dto.InvoiceTransportConfigLogDTO;

/**
 * <p>
 * 企业公里运价配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-11
 */
public interface InvoiceTransportPriceEnterpriseConfigService {

    InvoiceTransportConfigLogDTO getLastInvoiceTransportConfig(String serviceProviderCode);

    /**
     * 获取该企业目前正在生效的开票货源发货配置
     * @param enterpriseId 企业ID
     * @return
     */
    InvoiceTransportConfigLogDTO getLastInvoiceTransportEnterpriseConfig(Long enterpriseId, String serviceProviderCode);
}
