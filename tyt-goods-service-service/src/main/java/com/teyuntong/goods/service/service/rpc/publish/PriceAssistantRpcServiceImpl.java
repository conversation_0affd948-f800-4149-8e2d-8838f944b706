package com.teyuntong.goods.service.service.rpc.publish;

import com.teyuntong.goods.service.client.publish.dto.PriceAssistantDTO;
import com.teyuntong.goods.service.client.publish.service.PriceAssistantRpcService;
import com.teyuntong.goods.service.client.publish.vo.PriceAssistantVO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportQuotedPriceDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainExtendService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportQuotedPriceService;
import com.teyuntong.goods.service.service.common.enums.ExcellentGoodsEnums;
import com.teyuntong.goods.service.service.common.enums.InvoiceTransportEnum;
import com.teyuntong.goods.service.service.common.enums.YesOrNoEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.inner.PlatTransportRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.goods.service.service.rpc.publish.checker.AllowPriceChecker;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import com.teyuntong.inner.export.service.client.common.bean.ResultMsgBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.teyuntong.goods.service.service.common.constant.AbtestKeyConstant.PUBLISH_PRICE_ASSISTANT_ABTEST_CODE;
import static com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant.*;

/**
 * 发货回价助手
 *
 * <AUTHOR>
 * @since 2025-05-29 15:58
 */
@Slf4j
@RestController
public class PriceAssistantRpcServiceImpl implements PriceAssistantRpcService {

    @Resource
    private TransportMainExtendService transportMainExtendService;
    @Resource
    private TransportMainService transportMainService;
    @Resource
    private AllowPriceChecker allowPriceChecker;
    @Resource
    private TransportQuotedPriceService transportQuotedPriceService;

    @Resource
    private ABTestRemoteService abTestRemoteService;
    @Resource
    private TytConfigRemoteService tytConfigRemoteService;
    @Resource
    private PlatTransportRemoteService platTransportRemoteService;


    /**
     * 是否展示回价助手
     *
     * @param dto
     * @return
     */
    @Override
    public PriceAssistantVO showPriceAssistant(PriceAssistantDTO dto) {
        PriceAssistantVO vo = new PriceAssistantVO();
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();

        if (Objects.isNull(dto.getExcellentGoods()) || Objects.isNull(dto.getInvoiceTransport())) {
            return vo;
        }
        // 专车、专票货源不展示
        if (Objects.equals(dto.getExcellentGoods(), ExcellentGoodsEnums.SPECIAL.getCode()) ||
                Objects.equals(dto.getInvoiceTransport(), InvoiceTransportEnum.YES.getCode())) {
            return vo;
        }
        // 用户需要在回价助手ab测
        Integer userType = abTestRemoteService.getUserType(PUBLISH_PRICE_ASSISTANT_ABTEST_CODE, loginUser.getUserId());
        if (!Objects.equals(userType, YesOrNoEnum.YES.getId())) {
            return vo;
        }

        vo.setShowPriceAssistant(YesOrNoEnum.YES.getId());
        vo.setLabel(tytConfigRemoteService.getStringValue(PRICE_ASSISTANT_LABEL_KEY, "回价快 秒接单"));
        vo.setDescription(tytConfigRemoteService.getStringValue(PRICE_ASSISTANT_DESCRIPTION_KEY, "司机报价秒回复，避免错失走货机会"));
        vo.setPopupDescription(tytConfigRemoteService.getStringValue(PRICE_ASSISTANT_POPUP_DESCRIPTION_KEY, "司机报价后，5分钟内收不到回价，大概率就会接其他订单，使用回价助手，留住优质司机"));
        vo.setAddPriceRate(tytConfigRemoteService.getIntValue(PRICE_ASSISTANT_ADD_PRICE_RATE_KEY, 10));

        if (Objects.nonNull(dto.getSrcMsgId())) {
            TransportMainExtendDO mainExtendDO = transportMainExtendService.getBySrcMsgId(dto.getSrcMsgId());
            TransportMainDO main = transportMainService.getTransportMainForId(dto.getSrcMsgId());
            if (Objects.nonNull(main) && Objects.nonNull(mainExtendDO) && Objects.nonNull(mainExtendDO.getPriceCap())) {
                if (StringUtils.isBlank(main.getPrice()) ||
                        new BigDecimal(main.getPrice()).compareTo(new BigDecimal(mainExtendDO.getPriceCap())) < 0) {
                    vo.setPriceCap(mainExtendDO.getPriceCap());
                }
            }
        }

        return vo;
    }

    /**
     * 保存回价助手
     *
     * @param dto
     */
    @Override
    public Boolean savePriceAssistant(PriceAssistantDTO dto) {
        if (Objects.isNull(dto.getSrcMsgId()) || Objects.isNull(dto.getPriceCap())) {
            throw new BusinessException(GoodsErrorCode.ERROR_NO_PARAM);
        }
        TransportMainExtendDO mainExtendDO = transportMainExtendService.getBySrcMsgId(dto.getSrcMsgId());
        if (Objects.isNull(mainExtendDO)) {
            throw new BusinessException(GoodsErrorCode.ERROR_NO_TRANSPORT);
        }
        // 校验运费
        TransportMainDO main = transportMainService.getTransportMainForId(dto.getSrcMsgId());
        PublishBO publishBO = new PublishBO();
        BeanUtils.copyProperties(main, publishBO);
        publishBO.setPrice(dto.getPriceCap().toString());
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        allowPriceChecker.checkPublishPrice(publishBO, loginUser.getUserId());

        mainExtendDO.setPriceCap(dto.getPriceCap());
        mainExtendDO.setModifyTime(new Date());
        transportMainExtendService.updateMainExtendById(mainExtendDO);

        // 如果已有司机报价满足回价条件，按照报价金额从低到高逐个同意报价
        List<TransportQuotedPriceDO> priceDOList = transportQuotedPriceService.getCarQuotedPriceList(dto.getSrcMsgId(), loginUser.getUserId());
        if (CollectionUtils.isNotEmpty(priceDOList)) {
            List<TransportQuotedPriceDO> sortedPriceDOList = priceDOList.stream()
                    .filter(v -> Objects.nonNull(v.getCarQuotedPrice()) && v.getCarQuotedPrice() > 0)
                    .sorted(Comparator.comparingInt(TransportQuotedPriceDO::getCarQuotedPrice)).collect(Collectors.toList());
            for (TransportQuotedPriceDO priceDO : sortedPriceDOList) {
                transportAutoAgree(priceDO);
            }
        }
        return true;
    }

    @Async
    public void transportAutoAgree(TransportQuotedPriceDO priceDO) {
        try {
            ResultMsgBean resultMsgBean = platTransportRemoteService.transportAgree(priceDO.getTransportUserId(), priceDO.getId(), priceDO.getSrcMsgId(), 2);
            log.info("保存回价助手，货主自动同意司机报价, plat返回:{}", resultMsgBean);
        } catch (Exception e) {
            log.error("保存回价助手，货主自动同意司机报价异常，{}", priceDO.getSrcMsgId(), e);
        }
    }
}
