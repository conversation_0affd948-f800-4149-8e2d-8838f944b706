package com.teyuntong.goods.service.service.rpc.transport;

import com.teyuntong.goods.service.client.transport.service.TransportSyncYmmRpcService;
import com.teyuntong.goods.service.client.transport.vo.TytTransportSyncYmmVO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportSyncYmmDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportSyncYmmService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

/**
 * TYT YMM货源同步服务接口
 *
 * <AUTHOR>
 * @since 2024-01-17 16:08
 */
@RestController
@RequiredArgsConstructor
public class TransportSyncYmmRpcServiceImpl implements TransportSyncYmmRpcService {

    private final TransportSyncYmmService transportSyncYmmService;


    @Override
    public TytTransportSyncYmmVO getTransportSyncYmmByCargoId(Long cargoId) {
        TransportSyncYmmDO tytTransportSyncYmm = transportSyncYmmService.getTransportSyncYmmByCargoId(cargoId);
        TytTransportSyncYmmVO tytTransportSyncYmmVO = new TytTransportSyncYmmVO();
        if (tytTransportSyncYmm == null) {
            return null;
        }
        BeanUtils.copyProperties(tytTransportSyncYmm, tytTransportSyncYmmVO);
        return tytTransportSyncYmmVO;
    }

}
