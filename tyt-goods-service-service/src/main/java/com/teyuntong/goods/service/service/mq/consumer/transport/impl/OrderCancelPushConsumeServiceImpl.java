package com.teyuntong.goods.service.service.mq.consumer.transport.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainExtendService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.mq.consumer.OrderCancelPushConsumeService;
import com.teyuntong.goods.service.service.mq.pojo.OrderCancelMqBean;
import com.teyuntong.goods.service.service.remote.inner.GoodsPushRemoteService;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import com.teyuntong.inner.export.service.client.push.dto.GoodsPushDto;
import com.teyuntong.inner.export.service.client.push.enums.PushCodeEnum;
import com.teyuntong.inner.export.service.client.push.enums.PushTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.teyuntong.goods.service.service.common.constant.RedisKeyConstant.ORDER_CANCEL_RECOMMEND;

/**
 * 货源详情mq消费
 *
 * <AUTHOR>
 * @since 2025-04-10 10:53
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderCancelPushConsumeServiceImpl implements OrderCancelPushConsumeService {

    private final TransportMainService transportMainService;
    private final TransportMainExtendService transportMainExtendService;
    private final GoodsPushRemoteService goodsPushRemoteService;
    private final RedisUtil redisUtil;


    @Override
    public void consume(OrderCancelMqBean mqBean) {
        if (Objects.isNull(mqBean.getSrcMsgId()) || Objects.isNull(mqBean.getCarUserId())) {
            log.warn("撤销订单，mq参数缺失：{}", mqBean);
            return;
        }
        TransportMainDO transportMain = transportMainService.getTransportMainForId(mqBean.getSrcMsgId());
        if (transportMain == null) {
            log.warn("撤销订单，货源不存在：{}", mqBean);
            return;
        }

        try {
            Set<Long> excludeSrcMsgIds = redisUtil.membersLongSet(ORDER_CANCEL_RECOMMEND + mqBean.getCarUserId());
            if (CollUtil.isEmpty(excludeSrcMsgIds)) {
                excludeSrcMsgIds = new HashSet<>();
            }
            excludeSrcMsgIds.add(mqBean.getSrcMsgId());
            List<TransportMainDO> transportMainDOList = transportMainService.getSimilarityForOrderCancelPush(transportMain, new ArrayList<>(excludeSrcMsgIds));
            if (CollUtil.isEmpty(transportMainDOList)) {
                log.warn("撤销订单，没有找到相似的货源：{}", mqBean);
                return;
            }
            TransportMainDO pushMain = null;
            // 获取有价的货源
            List<TransportMainDO> havePriceMainList = transportMainDOList.stream().filter(main -> StringUtils.isNotBlank(main.getPrice()) && Integer.parseInt(main.getPrice()) > 0).toList();
            // 如果有多个有价货源，推送质量分最高的
            if (CollUtil.isNotEmpty(havePriceMainList)) {
                log.info("撤销订单，查询出的要推送的有价货源数量：{}", havePriceMainList.size());
                if (havePriceMainList.size() == 1) {
                    pushMain = havePriceMainList.get(0);
                } else {
                    List<Long> srcMsgIds = havePriceMainList.stream().map(TransportMainDO::getSrcMsgId).toList();
                    List<TransportMainExtendDO> extendDOList = transportMainExtendService.getBySrcMsgIds(srcMsgIds);
                    extendDOList.sort((o1, o2) -> o2.getGoodModelScore().compareTo(o1.getGoodModelScore()));
                    Long srcMsgId = extendDOList.get(0).getSrcMsgId();
                    pushMain = havePriceMainList.stream().filter(main -> main.getSrcMsgId().equals(srcMsgId)).findFirst().orElse(null);
                }
            } else {
                log.info("撤销订单，查询出的要推送的货源数量：{}", transportMainDOList.size());

                if (transportMainDOList.size() == 1) {
                    pushMain = havePriceMainList.get(0);
                } else {
                    List<Long> srcMsgIds = transportMainDOList.stream().map(TransportMainDO::getSrcMsgId).toList();
                    List<TransportMainExtendDO> extendDOList = transportMainExtendService.getBySrcMsgIds(srcMsgIds);
                    extendDOList.sort((o1, o2) -> o2.getGoodModelScore().compareTo(o1.getGoodModelScore()));
                    Long srcMsgId = extendDOList.get(0).getSrcMsgId();
                    pushMain = transportMainDOList.stream().filter(main -> main.getSrcMsgId().equals(srcMsgId)).findFirst().orElse(null);
                }
            }
            if (pushMain != null) {
                log.info("撤销订单，将要推送的货源：{}", pushMain.getSrcMsgId());
                pushTransport(pushMain, mqBean.getCarUserId());
                redisUtil.addSet(ORDER_CANCEL_RECOMMEND + mqBean.getCarUserId(), Duration.ofDays(1), pushMain.getSrcMsgId());
            } else {
                log.info("撤销订单，没有符合的要推送货源");

            }

        } catch (Exception e) {
            log.error("撤销订单推送异常：{}", mqBean, e);
        }

    }


    /**
     * 车主出价给货主发送消息
     *
     * @param main
     */
    private void pushTransport(TransportMainDO main, Long carUserId) {
        String startAddress = main.getStartCity() + main.getStartArea();
        String destAddress = main.getDestCity() + main.getDestArea();

        GoodsPushDto pushDto = new GoodsPushDto();
        pushDto.setSrcMsgId(main.getId());
        pushDto.setPushSource("撤销订单推送");
        pushDto.setTitle("推荐好货");
        String goodsName = StringUtils.isBlank(main.getGoodTypeName()) ? "货源" : main.getGoodTypeName();
        pushDto.setContent("为您推荐" + startAddress + "到" + destAddress + "的【" + goodsName + "】，点击立即抢货！");
        pushDto.setUserIdList(List.of(carUserId));
        pushDto.setPushType(PushTypeEnum.ALL_PUSH);
        pushDto.setPushCode(PushCodeEnum.RECOMMEND_GOODS_PUSH);
        JSONObject extraJson = new JSONObject();
        extraJson.put("srcMsgId", main.getSrcMsgId());
        extraJson.put("price", main.getPrice());
        extraJson.put("title", "推荐好货");
        extraJson.put("content", startAddress + "——" + destAddress);
        extraJson.put("infoText", goodsName + " " + main.getWeight() + "吨 "
                + Stream.of(main.getLength(), main.getWide(), main.getHigh()).filter(StringUtils::isNotBlank).collect(Collectors.joining("*")) + "米");
        pushDto.setExtraJson(extraJson.toString());
        goodsPushRemoteService.sendPush(pushDto);
    }


}
