package com.teyuntong.goods.service.service.biz.transport.service.impl;

import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportGoodModelFactorDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportGoodModelFactorMapper;
import com.teyuntong.goods.service.service.biz.transport.service.TransportGoodModelFactorService;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <p>
 * 好中差货模型因子 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportGoodModelFactorServiceImpl implements TransportGoodModelFactorService {

    private final TransportGoodModelFactorMapper transportGoodModelFactorMapper;

    /**
     * 运距阈值，单位：公里(km)，用于判断使用总价还是吨公里价
     */
    private final static BigDecimal DISTANCE_THRESHOLD = new BigDecimal(50);


    /**
     * 判断模型等级
     * 1. 满足5个前提规则门槛
     * - a.货源长<=13且宽<=3且高<=3
     * - b.货源价格>0
     * - c.货源吨重>0
     * - d.货源距离>0
     * - e.货源长非空且<>1，且宽非空且<>1，且高非空且<>1
     * 2. 若运距<=50km，则按照总价进行判断，反之按照吨公里价进行判断
     * - 注：价格区间判断时，也是左闭右开区间
     *
     * @return 0不合符
     */
    @Override
    public Integer judgeModelLevel(TransportMainDO transport) {
        // 不符合前提，返回0
        if (!checkTransport(transport)) {
            log.info("货源好中差货匹配规则，不符合条件：货源hashCode:{}", transport.getHashCode());
            return 0;
        }

        BigDecimal weight = new BigDecimal(transport.getWeight());
        BigDecimal distance = transport.getDistance();
        List<TransportGoodModelFactorDO> ruleList = transportGoodModelFactorMapper.matchRules(weight, distance);

        // 若运距<=50km，则按照总价进行判断，反之按照吨公里价进行判断
        boolean isMatchPrice = distance.compareTo(DISTANCE_THRESHOLD) <= 0;
        BigDecimal price = new BigDecimal(transport.getPrice());
        BigDecimal unitPrice = price.divide(weight.multiply(distance), 6, RoundingMode.HALF_UP);

        Integer modelLevel = ruleList.stream()
                .filter(rule -> {
                    if (isMatchPrice) {
                        return price.compareTo(rule.getPriceLower()) >= 0
                                && price.compareTo(rule.getPriceUpper()) < 0;
                    } else {
                        return unitPrice.compareTo(rule.getUnitPriceLower()) >= 0
                                && unitPrice.compareTo(rule.getUnitPriceUpper()) < 0;
                    }
                })
                .map(TransportGoodModelFactorDO::getModelLevel)
                .findFirst()
                .orElse(0);
        log.info("货源好中差货匹配规则：货源hashCode:{}, weight:{}, distance:{}, modelLevel:{}",
                transport.getHashCode(), weight, distance, modelLevel);
        return modelLevel;
    }

    private boolean checkTransport(TransportMainDO transport) {
        if (TransportUtil.hasPrice(transport.getPrice())
                && TransportUtil.hasWeight(transport.getWeight())
                && TransportUtil.hasDistance(transport.getDistance())) {
            return TransportUtil.isValidSize(transport.getLength(), "13")
                    && TransportUtil.isValidSize(transport.getWide(), "3")
                    && TransportUtil.isValidSize(transport.getHigh(), "3")
                    && TransportUtil.isValidSize(transport.getWeight(), "10000");
        }
        return false;
    }
}
