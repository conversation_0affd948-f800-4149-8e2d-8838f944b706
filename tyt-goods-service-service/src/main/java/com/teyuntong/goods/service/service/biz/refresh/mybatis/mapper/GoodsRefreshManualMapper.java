package com.teyuntong.goods.service.service.biz.refresh.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.service.service.biz.refresh.mybatis.entity.GoodsRefreshManualDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 货源刷新手动曝光表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-23
 */
@Mapper
public interface GoodsRefreshManualMapper extends BaseMapper<GoodsRefreshManualDO> {
    /**
     * 根据货源id查询
     *
     * @param srcMsgId
     * @return
     */
    GoodsRefreshManualDO getBySrcMsgId(@Param("srcMsgId") Long srcMsgId);
}
