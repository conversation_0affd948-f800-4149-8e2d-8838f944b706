package com.teyuntong.goods.service.service.rpc.publish.checker;

import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainExtendService;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.trade.service.client.orders.dto.CheckSeckillGoodsPayLimitDTO;
import com.teyuntong.trade.service.client.orders.service.TransportOrderSnapshotRpcService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 秒抢好货锁单校验
 *
 * <AUTHOR>
 * @since 2025/02/21 14:27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SeckillGoodsLockChecker {

    private final TransportOrderSnapshotRpcService transportOrderSnapshotRpcService;
    private final TransportMainExtendService transportMainExtendService;

    /**
     * 直接发布校验
     */
    public void check(DirectPublishProcessBO processBO) {
        PublishOptEnum directOptEnum = processBO.getOptEnum();

        // 填价、加价、转一口价、更新长宽高需要校验是否是秒抢货源
        if (directOptEnum == PublishOptEnum.FILL_PRICE
                || directOptEnum == PublishOptEnum.ADD_PRICE
                || directOptEnum == PublishOptEnum.TRANSFER_FIXED
                || directOptEnum == PublishOptEnum.UPDATE_INFO) {
            // 是秒抢货源
            TransportMainExtendDO mainExtend = processBO.getMainExtend();
            if (mainExtend != null && Objects.equals(mainExtend.getSeckillGoods(), 1)) {
                CheckSeckillGoodsPayLimitDTO payLimitDTO = transportOrderSnapshotRpcService.checkSeckillGoodsIsLock(mainExtend.getSrcMsgId());
                if (payLimitDTO != null && payLimitDTO.isLock()) {
                    throw new BusinessException(GoodsErrorCode.SECKILL_TRANSPORT_IS_LOCK);
                }
            }
        }
    }

    /**
     * 直接发布校验
     */
    public void check(Long srcMsgId) {
        // 是秒抢货源
        if (srcMsgId != null && srcMsgId > 0) {
            TransportMainExtendDO extendDO = transportMainExtendService.getBySrcMsgId(srcMsgId);
            if (extendDO != null) {
                if (Objects.equals(extendDO.getSeckillGoods(), 1)) {
                    CheckSeckillGoodsPayLimitDTO payLimitDTO = transportOrderSnapshotRpcService.checkSeckillGoodsIsLock(srcMsgId);
                    if (payLimitDTO != null && payLimitDTO.isLock()) {
                        throw new BusinessException(GoodsErrorCode.SECKILL_TRANSPORT_IS_LOCK);
                    }
                }
            }
        }
    }
}
