package com.teyuntong.goods.service.service.rpc.publish.checker;

import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.common.enums.ExcellentGoodsEnums;
import com.teyuntong.goods.service.service.common.enums.PublishStyleEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.remote.basic.NoticePopupTemplRemoteService;
import com.teyuntong.goods.service.service.remote.market.CouponRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserPermissionRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishProcessBO;
import com.teyuntong.infra.basic.resource.client.popup.dto.PopupTypeEnum;
import com.teyuntong.infra.basic.resource.client.popup.vo.NoticePopupTemplVo;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.market.activity.client.coupon.vo.VipNoticeForGoodsVo;
import com.teyuntong.user.service.client.permission.dto.AuthPermissionRpcDTO;
import com.teyuntong.user.service.client.permission.enums.ServicePermissionEnum;
import com.teyuntong.user.service.client.permission.vo.AuthPermissionRpcVO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 校验小程序发货有效性
 *
 * <AUTHOR>
 * @since 2025/02/21 14:27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PublishPermissionChecker {

    private final UserPermissionRemoteService userPermissionRemoteService;
    private final CouponRemoteService couponRemoteService;
    private final NoticePopupTemplRemoteService noticePopupTemplRemoteService;

    /**
     * 校验用户发货权益并进行扣除
     *
     * @param publishProcessBO
     * @param deduction        是否扣除权益
     */
    public void checkPublishPermission(PublishProcessBO publishProcessBO, boolean deduction) {
        PublishBO publishBO = publishProcessBO.getPublishBO();
        UserRpcVO user = publishProcessBO.getUser();
        TransportMainDO transportMain = publishProcessBO.getTransportMain();
        // 当日货源编辑发布不校验权益
        if (Objects.equals(publishBO.getPublishStyle(), PublishStyleEnum.TODAY_PUBLISH.getCode())) {
            return;
        }
        // 专车和优车发货卡不校验发货权益
        if (Objects.equals(publishBO.getExcellentGoods(), ExcellentGoodsEnums.SPECIAL.getCode()) || publishBO.getExcellentCardId() != null) {
            return;
        }
        Long srcMsgId = transportMain != null ? transportMain.getSrcMsgId() : null;
        AuthPermissionRpcVO authPermissionRpcVO = authPermission(publishBO.getPrice(), srcMsgId, user.getId(), deduction);

        publishProcessBO.setAuthPermissionRpcVO(authPermissionRpcVO);

    }


    /**
     * 校验用户发货权益并进行扣除
     *
     * @param directPublishProcessBO
     * @param deduction              是否扣除权益
     */
    public void checkDirectPublishPermission(DirectPublishProcessBO directPublishProcessBO, boolean deduction) {
        UserRpcVO user = directPublishProcessBO.getUser();
        DirectPublishBO directPublishBO = directPublishProcessBO.getDirectPublishBO();
        TransportMainDO oldMain = directPublishProcessBO.getOldMain();
        TransportMainDO transportMain = directPublishProcessBO.getTransportMain();
        // 当日货源编辑发布不校验权益
        if (!directPublishBO.isHistoryGoods()) {
            return;
        }
        // 专车和优车发货卡不校验发货权益
        if (Objects.equals(oldMain.getExcellentGoods(), ExcellentGoodsEnums.SPECIAL.getCode()) || directPublishBO.getExcellCardId() != null) {
            return;
        }
        Long srcMsgId = transportMain != null ? transportMain.getSrcMsgId() : null;
        AuthPermissionRpcVO authPermissionRpcVO = authPermission(oldMain.getPrice(), srcMsgId, user.getId(), deduction);

        directPublishProcessBO.setAuthPermissionRpcVO(authPermissionRpcVO);

    }

    /**
     * 校验并扣减发货权益
     *
     * @param price
     * @param userId
     * @param price
     * @param srcMsgId
     * @param userId
     * @param deduction 是否扣除权益
     * @return
     */
    private AuthPermissionRpcVO authPermission(String price, Long srcMsgId, Long userId, boolean deduction) {

        AuthPermissionRpcDTO permissionRpcDTO = new AuthPermissionRpcDTO();
        if (StringUtils.isNotBlank(price)) {
            permissionRpcDTO.setServicePermissionEnum(ServicePermissionEnum.有价货源发布);
        } else {
            permissionRpcDTO.setServicePermissionEnum(ServicePermissionEnum.货源发布);
        }
        permissionRpcDTO.setUserId(userId);
        permissionRpcDTO.setSrcMsgId(srcMsgId);
        AuthPermissionRpcVO authPermissionRpcVO = new AuthPermissionRpcVO();
        if (deduction) {
            authPermissionRpcVO = userPermissionRemoteService.authPermission(permissionRpcDTO);
        } else {
            authPermissionRpcVO = userPermissionRemoteService.checkAuthPermission(permissionRpcDTO);
        }
        if (!authPermissionRpcVO.isUse()) {
            VipNoticeForGoodsVo noticeVo = couponRemoteService.getVipNoticeForGoodsPublish(userId);
            if (noticeVo != null) {
                throw new BusinessException(GoodsErrorCode.PUBLISH_NO_PERMISSION_NOTICE, noticeVo);
            } else {
                PopupTypeEnum popupTypeEnum = PopupTypeEnum.getByName(authPermissionRpcVO.getPermissionPopupTypeEnum().name());
                NoticePopupTemplVo noticePopupTemplVo = noticePopupTemplRemoteService.getByType(popupTypeEnum);
                throw new BusinessException(CommonErrorCode.NOTICE_DATA_POP, noticePopupTemplVo);
            }
        }
        return authPermissionRpcVO;
    }


}
