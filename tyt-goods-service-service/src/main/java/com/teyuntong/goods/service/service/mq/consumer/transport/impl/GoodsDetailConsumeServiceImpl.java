package com.teyuntong.goods.service.service.mq.consumer.transport.impl;

import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.TransportViewLogDO;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.TransportDispatchViewService;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.TransportViewLogService;
import com.teyuntong.goods.service.service.biz.log.mybatis.entity.CreeperAppRequestLogDO;
import com.teyuntong.goods.service.service.biz.log.mybatis.entity.LogTsDetailsDO;
import com.teyuntong.goods.service.service.biz.log.service.CreeperAppRequestLogService;
import com.teyuntong.goods.service.service.biz.log.service.LogTsDetailsService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.common.utils.TimeUtil;
import com.teyuntong.goods.service.service.mq.consumer.transport.GoodsDetailConsumeService;
import com.teyuntong.goods.service.service.mq.pojo.GoodsDetailMqBean;
import com.teyuntong.goods.service.service.remote.user.UserRemoteService;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 货源详情mq消费
 *
 * <AUTHOR>
 * @since 2025-04-10 10:53
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GoodsDetailConsumeServiceImpl implements GoodsDetailConsumeService {

    private final TransportDispatchViewService transportDispatchViewService;
    private final UserRemoteService userRemoteService;
    private final TransportMainService transportMainService;
    private final LogTsDetailsService logTsDetailsService;
    private final TransportViewLogService transportViewLogService;
    private final CreeperAppRequestLogService creeperAppRequestLogService;

    @Override
    public void consume(GoodsDetailMqBean mqBean) {
        if (Objects.isNull(mqBean.getUserId()) || Objects.isNull(mqBean.getGoodsId())) {
            log.warn("getSingleDetail货源详情，mq参数缺失：{}", mqBean);
            return;
        }
        // 反爬日志记录
        logCreeperAppRequestLog(mqBean);

        UserRpcVO user = userRemoteService.getUser(mqBean.getUserId());
        TransportMainDO main = transportMainService.getTransportMainForId(mqBean.getGoodsId());
        if (Objects.nonNull(user) && Objects.nonNull(main)) {
            if (!Objects.equals(mqBean.getUserId(), main.getUserId())) {
                // 保存货源查看记录
                logTransportDispatchView(user, main);

                String today = TimeUtil.formatDate(new Date());
                String ctime = TimeUtil.formatDate(mqBean.getPublishTime());
                if (today.equals(ctime)) {
                    // tyt_log库查看记录
                    logTsDetail(mqBean);
                    // tyt_transport_view_log记录
                    logTransportViewLog(mqBean);
                }
            }
        }
    }

    private void logCreeperAppRequestLog(GoodsDetailMqBean mqBean) {
        CreeperAppRequestLogDO logDO = new CreeperAppRequestLogDO();
        logDO.setUserId(mqBean.getUserId());
        logDO.setSrcMsgId(mqBean.getGoodsId());
        logDO.setType(1);
        logDO.setCreateTime(mqBean.getViewTime());
        creeperAppRequestLogService.save(logDO);
    }

    private void logTransportViewLog(GoodsDetailMqBean mqBean) {
        TransportViewLogDO viewLogDO = new TransportViewLogDO();
        viewLogDO.setTsId(mqBean.getGoodsId());
        viewLogDO.setUserId(mqBean.getUserId());
        viewLogDO.setClientSign(mqBean.getClientSign());
        viewLogDO.setClientVersion(mqBean.getClientVersion());
        transportViewLogService.saveTransportViewLog(viewLogDO);
    }

    private void logTsDetail(GoodsDetailMqBean mqBean) {
        LogTsDetailsDO detailsDO = new LogTsDetailsDO();
        detailsDO.setUserId(mqBean.getUserId());
        detailsDO.setTsId(mqBean.getGoodsId());
        detailsDO.setClientVersion(mqBean.getClientVersion());
        detailsDO.setClientSign(mqBean.getClientSign());
        detailsDO.setStatus(mqBean.getStatus());
        detailsDO.setViewSource(Objects.isNull(mqBean.getViewSource()) ? 0 : mqBean.getViewSource());
        detailsDO.setSortType(mqBean.getSortType());
        detailsDO.setSortIndex(Objects.isNull(mqBean.getSortIndex()) ? 0 : mqBean.getSortIndex());
        detailsDO.setSpecialMark(mqBean.getSpecialMark());
        detailsDO.setStartProvinc(mqBean.getStartProvinc());
        detailsDO.setStartCity(mqBean.getStartCity());
        detailsDO.setStartArea(mqBean.getStartArea());
        detailsDO.setDestProvinc(mqBean.getDestProvinc());
        detailsDO.setDestCity(mqBean.getDestCity());
        detailsDO.setDestArea(mqBean.getDestArea());
        detailsDO.setCtime(Objects.isNull(mqBean.getViewTime()) ? new Date() : mqBean.getViewTime());
        detailsDO.setBenefitLabelCode(mqBean.getBenefitLabelCode());
        logTsDetailsService.saveLogTsDetails(detailsDO);
    }

    /**
     * 保存查看记录
     *
     * @param user
     * @param mainDO
     */
    private void logTransportDispatchView(UserRpcVO user, TransportMainDO mainDO) {
        transportDispatchViewService.addTransportView(user, mainDO.getSrcMsgId(), 1);
    }
}
