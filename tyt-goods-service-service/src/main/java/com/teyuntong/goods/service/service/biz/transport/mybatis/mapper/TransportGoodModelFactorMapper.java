package com.teyuntong.goods.service.service.biz.transport.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportGoodModelFactorDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 好中差货模型因子 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Mapper
public interface TransportGoodModelFactorMapper extends BaseMapper<TransportGoodModelFactorDO> {

    List<TransportGoodModelFactorDO> matchRules(@Param("weight") BigDecimal weight,
                                                @Param("distance") BigDecimal distance);
}
