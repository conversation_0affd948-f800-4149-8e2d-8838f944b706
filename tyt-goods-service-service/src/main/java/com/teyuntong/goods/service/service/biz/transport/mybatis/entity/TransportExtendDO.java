package com.teyuntong.goods.service.service.biz.transport.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@TableName("tyt_transport_extend")
public class TransportExtendDO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 货源ID
     */
    private Long srcMsgId;

    /**
     * transport表ID
     */
    private Long tsId;

    /**
     * 用车类型：1-整车，2-零担
     */
    private Integer useCarType;

    /**
     * 运价模式：1-固定运价，2-灵活运价
     */
    private Integer priceType;

    /**
     * 最小建议价
     */
    private Integer suggestMinPrice;

    /**
     * 最大建议价
     */
    private Integer suggestMaxPrice;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 好货模型分数
     */
    private BigDecimal goodModelScore;

    /**
     * 好货模型等级
     */
    private Integer goodModelLevel;


    /**
     * 好差货模型分数
     */
    private BigDecimal limGoodModelScore;

    /**
     * 好差货模型等级
     */
    private Integer limGoodModelLevel;

    /**
     * 回价助手运费上限
     */
    private Integer priceCap;

    /**
     * 好货运价模型分数（抽佣）
     */
    private BigDecimal commissionScore;

    /**
     * 置顶方式：1手动编辑发布；2直接发布(包括加价，自动重发)；3曝光卡；4自动刷新置顶；
     */
    private Integer topFlag;

    /**
     * 秒抢货源：1是0否
     */
    private Integer seckillGoods;

    /**
     * 优惠金额
     */
    private Integer perkPrice;


    /**
     * 好货标签 11:好1，12:好2，13:好3，21:中1，22:中2，23:中3，31:差1，32:差2，0:不符合
     */
    private Integer goodTransportLabel;
}