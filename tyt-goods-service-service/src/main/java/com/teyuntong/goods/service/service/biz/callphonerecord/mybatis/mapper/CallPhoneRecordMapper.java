package com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper;

import com.teyuntong.goods.service.service.biz.callphonerecord.bean.GoodsContactTimeDTO;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.CallPhoneRecordDO;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.CallPhoneRecordTransportCountDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface CallPhoneRecordMapper {

    List<CallPhoneRecordTransportCountDO> getRecordBySrcMsgIdList(@Param("srcMsgIdList") List<Long> srcMsgIdList);

    List<GoodsContactTimeDTO> getGoodsContactTimeByRoute(@Param("startCity") String startCity,
                                                         @Param("destCity") String destCity,
                                                         @Param("startTime") Date startTime);

    CallPhoneRecordDO getLatestCallRecord(@Param("srcMsgIds") List<Long> srcMsgIds);

    List<CallPhoneRecordDO> getCallPhoneRecords (@Param("srcMsgId") Long srcMsgId);

    String getCallPhoneRecordsRemark (@Param("srcMsgId") Long srcMsgId, @Param("carUserId") Long carUserId);

}