package com.teyuntong.goods.service.service.biz.transport.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.ChangePriceLogDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 价格变动日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-01
 */
@Mapper
public interface ChangePriceLogMapper extends BaseMapper<ChangePriceLogDO> {

    /**
     * 根据货源ID查询最近的一条价格变动记录
     *
     * @param srcMsgId 货源ID
     * @return 价格变动记录
     */
    ChangePriceLogDO getLatestLogBySrcMsgId(@Param("srcMsgId") Long srcMsgId);
}
