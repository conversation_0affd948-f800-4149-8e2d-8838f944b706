package com.teyuntong.goods.service.service.biz.callphonerecord.service.impl;

import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.TransportViewLogDO;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper.TransportViewLogMapper;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.TransportViewLogService;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 货物详情浏览日志表，每人每货仅存储一条 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportViewLogServiceImpl implements TransportViewLogService {
    private final TransportViewLogMapper transportViewLogMapper;

    @Override
    public boolean isViewed(Long userId, Long srcMsgId) {
        int count = transportViewLogMapper.isViewed(userId, srcMsgId);
        return count > 0;
    }

    @Override
    public void saveTransportViewLog(TransportViewLogDO viewLogDO) {
        transportViewLogMapper.insertIgnore(viewLogDO);
    }
}
