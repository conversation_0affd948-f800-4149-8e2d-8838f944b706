package com.teyuntong.goods.service.service.biz.transport.mybatis.mapper;

import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportQuotedPriceDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 无价货源报价表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-11-05
 */
@Mapper
public interface TransportQuotedPriceMapper extends BaseMapper<TransportQuotedPriceDO> {

    TransportQuotedPriceDO getQuotedPriceByCarUserIdAndTransportMainId(@Param("carUserId") Long carUserId,
                                                                       @Param("srcMsgId") Long srcMsgId);

    List<TransportQuotedPriceDO> getQuotedPriceListByTransportMainId(@Param("srcMsgId") Long srcMsgId);

    Integer getTransportQuotedPriceCountBySrcMsgId(@Param("srcMsgId") Long srcMsgId);

    Integer isFreightDepot(@Param("userId") Long userId);

    Integer getCarIsHaveQuotedPriceToTransport(@Param("carUserId") Long carUserId,
                                               @Param("srcMsgId") Long srcMsgId);
    int getTransportHaveOptionQuotedPriceCount(@Param("srcMsgId") Long srcMsgId);

    Long firstCarQuotedPrice(@Param("carUserId") Long carUserId, @Param("carUserName") String carUserName
            , @Param("transportUserId") Long transportUserId, @Param("transportUserName") String transportUserName
            , @Param("srcMsgId") Long srcMsgId, @Param("price") Integer price, @Param("reason") String reason);

    Long firstCarQuotedPriceV2(TransportQuotedPriceDO quotedPriceDO);

    void transportAgree(@Param("transportQuotedPriceId") Long transportQuotedPriceId, @Param("priceAssistantAutoAgree") Integer priceAssistantAutoAgree);

    void transportQuotedPrice(@Param("transportQuotedPriceId") Long transportQuotedPriceId, @Param("price") Integer price);

    TransportQuotedPriceDO getQuotedPriceLastOneBySrcMsgId(@Param("srcMsgId") Long srcMsgId);

    int countSystemQuotedPrice(@Param("srcMsgId") Long srcMsgId);

    List<TransportQuotedPriceDO> getAllQuotedPriceListBySrcMsgIdList(@Param("srcMsgIds") List<Long> transportIdList);

    List<TransportQuotedPriceDO> getCarQuotedPriceList(@Param("srcMsgId") Long srcMsgId, @Param("transportUserId") Long transportUserId);

    TransportQuotedPriceDO getLatestQuotedRecord(@Param("srcMsgIds") List<Long> srcMsgIds);

    void subsequentCarQuotedPrice(@Param("carUserId") Long carUserId, @Param("carUserName") String carUserName
            , @Param("srcMsgId") Long srcMsgId, @Param("price") Integer price, @Param("reason") String reason);

    void carAgree(@Param("carUserId") Long carUserId, @Param("srcMsgId") Long srcMsgId);

    List<Long> getTransportHaveAnyQuotedPrice(@Param("transportUserId") Long transportUserId);

    List<Long> getCarHaveNewTransportQuotedPrice(@Param("carUserId") Long carUserId);
}
