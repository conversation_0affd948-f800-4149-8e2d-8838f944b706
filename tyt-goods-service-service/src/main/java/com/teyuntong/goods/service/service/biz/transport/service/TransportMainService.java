package com.teyuntong.goods.service.service.biz.transport.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.teyuntong.goods.service.client.transport.dto.TransportCountDTO;
import com.teyuntong.goods.service.client.transport.dto.TransportListDTO;
import com.teyuntong.goods.service.client.transport.dto.TransportSimilarityDTO;
import com.teyuntong.goods.service.client.transport.vo.*;
import com.teyuntong.goods.service.service.biz.callphonerecord.bean.TransportLabelJson;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;
import com.teyuntong.user.service.client.permission.vo.AuthPermissionRpcVO;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 运输信息表主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
public interface TransportMainService extends IService<TransportMainDO> {

    /**
     * 车货签署协议更改货源信息服务接口
     *
     * @param transportMainDO 货源信息
     */
    void updateCarAgreementAboutTransportMainSomeFieldBySrcMsgId(TransportMainDO transportMainDO);

    /**
     * 查询过滤发布中状态货源ID
     *
     * @param haveQuotedPriceSrcMsgIdList
     * @return
     */
    List<Long> getInReleaseTransport(List<Long> haveQuotedPriceSrcMsgIdList);

    /**
     * 根据货源ID查询货源是否存在，如果存在，返回值中将有ID信息
     *
     * @param srcMsgId 货源ID
     */
    TransportMainDO isHaveTransportMainBySrcMsgIdAndStatus(Long srcMsgId);

    /**
     * 获取货主发布中的开票货源ID集合
     *
     * @param userId 货主ID
     * @return 货源ID
     */
    List<Long> getInReleaseInvoiceTransportSrcMsgIdList(Long userId);

    /**
     * 获取货主发布中的货源ID集合
     *
     * @param userId 货主ID
     * @return 货源ID
     */
    List<Long> getInReleaseTransportIdList(Long userId);

    TransportDynamicVO getTransportDynamic(Long userId, int times);

    /**
     * 货源列表
     *
     * @param dto
     * @return
     */
    MyTransportVO getMyPublish(TransportListDTO dto);

    /**
     * 返回货源扩展字段
     */
    List<TransportMainExtendDO> getExtendList(List<Long> srcMsgIds);

    CheckIsNeedFreeTecServiceFeeVO checkIsNeedFreeTecSericeFeeByCarUser(Long userId, Long srcMsgId);

    CheckIsNeedFreeTecServiceFeeVO checkIsNeedFreeTecSericeFeeByTransport(Long transportUserId, String startCity, boolean isGoodCarPriceTransport);

    TransportMainDO getTransportMainForId(Long srcMsgId);

    /**
     * 判断该用户上一票发的货是否是开票货源，如果是则返回开票主体ID，如果不是则返回null
     *
     * @param userId 货主ID
     * @return 开票主体ID
     */
    Long getLastTransportInvoiceSubjectId(Long userId);


    void initRecordGoodsTransactionInfo(TransportMainDO transportMainForId);

    /**
     * 获取上一次成交的相同货源信息
     */
    TransportMainDO getLastSameDealTransport(TransportMainDO transportMainDO);

    /**
     * 生成相似货源code
     */
    String genSimilarityCode(TransportMainDO transportMainDO);

    /**
     * 统计相似货源数
     *
     * @param similarityCode 相似货源code
     * @param srcMsgId       如果不为空，则排除当前货源
     */
    int countSimilarityGoods(String similarityCode, Long srcMsgId);

    /**
     * 查询货源相似货源数量（发布中状态）
     *
     * @param srcMsgId
     * @return
     */
    int countSimilarityGoods(Long srcMsgId);

    /**
     * 查询同步线货源数量（发布中状态）
     *
     * @param srcMsgId
     * @return
     */
    int countSameRouteGoods(Long srcMsgId);

    List<String> selectOfPublishType(Long userId, Date publishTime);

    Date hasUserTransportLast30Day(Long userId);

    List<TransportMainDO> getUserSomeDayTransportData(Long userId, String lastPublishDay);

    Integer getPublishCountByUserId(Long userId, Date startTime, Date endTime);

    List<TransportMainDO> getUserCommissionGoods(Long userId);

    SimilarityTransportTurnoverRatioVO getSimilarityTransportTurnoverRatio(TransportSimilarityDTO similarityDTO);

    void initGoodsRefresh(AuthPermissionRpcVO authPermissionRpcVO, Integer maxResendCounts, MyTransportListVO vo);

    Boolean getSimilarityTransportHavePriceCount(String similarityCode);


    Integer selectDistanceBySrcMsgId(Long srcMsgId);

    /**
     * 校验重货
     *
     * @param newHashCode 必须使用新的hashCode
     */
    boolean checkDuplicateTransport(String newHashCode, TransportMainDO transportMainDO);

    /**
     * 修改货源信用曝光
     *
     * @param srcMsgId
     * @param creditRetop
     */
    void saveTransportCreditExposure(Long srcMsgId, Integer creditRetop);

    /**
     * 获取相似货源首条货源
     */
    TransportMainDO getFirstSimilarityGoods(String similarityCode);

    /**
     * 将货源设置为不显示状态
     *
     * @param srcMsgId
     */
    void noDisplay(Long srcMsgId);

    TransportLabelJson getTransportLabelJson(Long srcMsgId);

    /**
     * 将货源设置成无效状态
     *
     * @param srcMsgId
     */
    void invalidTransport(Long srcMsgId);

    /**
     * 校验是否是参与现金奖活动货源
     * a. 有抽佣标签的货源且技术服务费>0
     * b. 经分口径下的直客或物流公司的发布的前3个货源
     * c. 经分口径下的直客或物流公司的货源，距离首发时间超过2小时还未成交的货源
     */
    boolean isCashPrizeActivityTransport(TransportMainDO transportMain);

    /**
     * 用户当天发布中货源量
     *
     * @param userId
     * @return
     */
    int getUserDayPublishCount(Long userId);

    /**
     * 校验用户是否是货主
     *
     * @param userId
     * @param srcMsgId
     * @return
     */
    boolean checkUserIsTransportOwner(Long userId, Long srcMsgId);

    /**
     * 查询用户货源数量
     *
     * @param transportCountDTO
     * @return
     */
    int getTransportCountForUserId(TransportCountDTO transportCountDTO);

    /**
     * 隐藏历史货源，不显示
     *
     * @param srcMsgId
     */
    void hideHistoryTransport(Long srcMsgId);

    void updateStatusById(Long srcMsgId, Integer status, String infoStatus, int display);

    void updateLabelJsonBySrcMsgId(Long srcMsgId, String jsonString);

    /**
     * 获取用户上一单
     *
     * @param userId
     * @return
     */
    TransportMainDO getLastTransport(Long userId);

    /**
     * 批量查询货源
     */
    List<TransportMainDO> getBySrcMsgIds(List<Long> srcMsgIds);

    /**
     * 获取当前用户发布的今日货源
     */
    List<TransportMainDO> getTodayPublishTransport(Long userId);

    /**
     * 更新货源
     *
     * @param transportMain
     */
    int updateTransportMain(TransportMainDO transportMain);

    /**
     * 删除我的货源
     */
    void deleteMyGoods(Long srcMsgId);

    /**
     * 获取相似货源，用于取消订单时推送相似货源
     *
     * @param transportMain
     * @return
     */
    List<TransportMainDO> getSimilarityForOrderCancelPush(TransportMainDO transportMain, List<Long> excludeSrcMsgIds);

    List<Long> getTransportForUser(Long userId, Integer status, Date startTime, Date endTime);
}
