package com.teyuntong.goods.service.service.biz.commission.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.sql.Time;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 货源抽佣规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
@Getter
@Setter
@TableName("tyt_draw_commission_rule")
public class DrawCommissionRuleDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 抽佣渠道 1：普货 + 优车,3优车定价,4运满满,5专车
     */
    private Integer commissionSource;

    /**
     * 货源类型（电议1，一口价2）
     */
    private Integer publishType;

    /**
     * 有无价格（0无价，1有价）
     */
    private Integer havePrice;

    /**
     * 订金是否退还（0不退还；1退还）
     */
    private Integer refundFlag;

    /**
     * 抽佣开始日期
     */
    private Date startDate;

    /**
     * 抽佣结束日期
     */
    private Date endDate;

    /**
     * 每天开始时间点
     */
    private Time dailyStart;

    /**
     * 每天结束时间点
     */
    private Time dailyEnd;

    /**
     * 抽佣货源上限,为空代表没有上限
     */
    private Integer commissionMaxCount;

    /**
     * 抽取规则 0:全量抽取 1：隔条抽取
     */
    private Integer rule;

    /**
     * 0：禁用 1：启用
     */
    private Integer enabled;

    /**
     * 是否接入好货模型分数 0：否 1：是
     */
    private Integer goodTransport;

    /**
     * 好货分数线最小值
     */
    private BigDecimal minFractionalLine;

    /**
     * 好货分数线最大值
     */
    private BigDecimal maxFractionalLine;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人id
     */
    private Long createId;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 修改人id
     */
    private Long modifyId;

    /**
     * 修改人名称
     */
    private String modifyName;
}
