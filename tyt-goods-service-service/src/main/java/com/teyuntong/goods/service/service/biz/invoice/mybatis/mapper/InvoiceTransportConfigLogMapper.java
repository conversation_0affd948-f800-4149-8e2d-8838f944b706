package com.teyuntong.goods.service.service.biz.invoice.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.service.service.biz.invoice.mybatis.entity.InvoiceTransportConfigLogDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-11
 */
@Mapper
public interface InvoiceTransportConfigLogMapper extends BaseMapper<InvoiceTransportConfigLogDO> {

    InvoiceTransportConfigLogDO getLastConfig(@Param("ServiceProviderCodeTypeList") List<Integer> ServiceProviderCodeTypeList);
}
