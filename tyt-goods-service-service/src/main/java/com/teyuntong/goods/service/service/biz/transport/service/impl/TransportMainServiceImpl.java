package com.teyuntong.goods.service.service.biz.transport.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teyuntong.goods.service.client.transport.dto.TransportCountDTO;
import com.teyuntong.goods.service.client.transport.dto.TransportListDTO;
import com.teyuntong.goods.service.client.transport.dto.TransportSimilarityDTO;
import com.teyuntong.goods.service.client.transport.enums.QueryMenuTypeEnum;
import com.teyuntong.goods.service.client.transport.vo.*;
import com.teyuntong.goods.service.service.biz.callphonerecord.bean.TransportLabelJson;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.TransportDispatchViewDO;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper.TransportDispatchViewMapper;
import com.teyuntong.goods.service.service.biz.exposure.service.ExposureCardGiveawayService;
import com.teyuntong.goods.service.service.biz.goodsname.mybatis.entity.MachineTypeBrandNewDO;
import com.teyuntong.goods.service.service.biz.goodsname.service.MachineTypeBrandNewService;
import com.teyuntong.goods.service.service.biz.goodsrecord.mybatis.mapper.CustomFirstOrderRecordMapper;
import com.teyuntong.goods.service.service.biz.order.service.TransportAfterOrderDataService;
import com.teyuntong.goods.service.service.biz.transport.dto.TransportDoneListDTO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.*;
import com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.*;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportQuotedPriceService;
import com.teyuntong.goods.service.service.common.constant.RedisKeyConstant;
import com.teyuntong.goods.service.service.common.enums.DwsIdentityTypeEnum;
import com.teyuntong.goods.service.service.common.enums.PublishTypeEnum;
import com.teyuntong.goods.service.service.common.utils.CityUtil;
import com.teyuntong.goods.service.service.common.utils.MD5Util;
import com.teyuntong.goods.service.service.common.utils.TimeUtil;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.order.InfoFeeRemoteService;
import com.teyuntong.goods.service.service.remote.order.OrdersRemoteService;
import com.teyuntong.goods.service.service.remote.user.ThirdEnterpriseRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserPermissionRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserRemoteService;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import com.teyuntong.trade.service.client.infofee.dto.UserPerformanceNumDTO;
import com.teyuntong.trade.service.client.orders.vo.TsOrderCountVO;
import com.teyuntong.user.service.client.enterprise.vo.InvoiceDominantVo;
import com.teyuntong.user.service.client.permission.dto.AuthPermissionRpcDTO;
import com.teyuntong.user.service.client.permission.enums.ServicePermissionEnum;
import com.teyuntong.user.service.client.permission.vo.AuthPermissionRpcVO;
import com.teyuntong.user.service.client.user.vo.DwsNewIdentiwoDataRpcVO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.teyuntong.goods.service.service.common.constant.RedisKeyConstant.CACHE_EXPIRE_LOCATION_APPLY;
import static com.teyuntong.goods.service.service.common.constant.RedisKeyConstant.FREIGHT_ADD_MONEY_NUM;

/**
 * <p>
 * 运输信息表主表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportMainServiceImpl extends ServiceImpl<TransportMainMapper, TransportMainDO> implements TransportMainService {

    private final TransportMainMapper transportMainMapper;

    private final TransportMapper transportMapper;

    private final TransportDispatchViewMapper transportDispatchViewMapper;

    private final TransportMainExtendMapper transportMainExtendMapper;

    private final UserPermissionRemoteService userPermissionRemoteService;

    private final TransportBackendMapper transportBackendMapper;

    private final BackoutReasonMapper backoutReasonMapper;

    private final TransportDoneMapper transportDoneMapper;

    private final TransportEnterpriseLogMapper transportEnterpriseLogMapper;
    @Lazy
    @Autowired
    private TransportAfterOrderDataService transportAfterOrderDataService;

    private final StringRedisTemplate stringRedisTemplate;

    private final UserRemoteService userRemoteService;

    private final OrdersRemoteService ordersRemoteService;

    private final TytConfigRemoteService tytConfigRemoteService;

    private final InfoFeeRemoteService InfoFeeRemoteService;

    private final TransportQuotedPriceService transportQuotedPriceService;

    private final ABTestRemoteService abTestRemoteService;

    private final ThPriceServiceImpl thPriceService;

    private final RedisUtil redisUtil;

    private final TytFreeTecServiceFeeCarUserLogMapper tytFreeTecServiceFeeCarUserLogMapper;

    private final MachineTypeBrandNewService machineTypeBrandNewService;

    private final ExposureCardGiveawayService exposureCardGiveawayService;
    private final CustomFirstOrderRecordMapper customFirstOrderRecordMapper;

    private final ThirdEnterpriseRemoteService thirdEnterpriseRemoteService;

    public static final String TransportDynamicRedisKey = "transport_dynamic";

    public static final String TransportDynamicTimesRedisKey = "transport_dynamic:times";

    public static final String TransportDynamicShowedTransportRedisKey = "transport_dynamic:showed";

    @Override
    public void updateCarAgreementAboutTransportMainSomeFieldBySrcMsgId(TransportMainDO transportMainDO) {
        transportMainMapper.updateCarAgreementAboutTransportMainSomeFieldBySrcMsgId(transportMainDO);
    }

    @Override
    public List<Long> getInReleaseTransport(List<Long> srcMsgIdList) {
        return transportMainMapper.getInReleaseTransport(srcMsgIdList);
    }

    @Override
    public TransportMainDO isHaveTransportMainBySrcMsgIdAndStatus(Long srcMsgId) {
        return transportMainMapper.isHaveTransportMainBySrcMsgIdAndStatus(srcMsgId);
    }

    @Override
    public List<Long> getInReleaseInvoiceTransportSrcMsgIdList(Long userId) {
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        Date todayStartDate = Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
        return transportMainMapper.getInReleaseInvoiceTransportSrcMsgIdList(userId, todayStartDate);
    }

    @Override
    public List<Long> getInReleaseTransportIdList(Long userId) {
        return transportMainMapper.getInReleaseTransportIdList(userId);
    }

    @Override
    public TransportDynamicVO getTransportDynamic(Long userId, int times) {
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        Date todayStartDate = Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());

        //获取所有发布中的货源
        List<Long> inReleaseAndNoPriceTransportSrcMsgIdList = transportMainMapper.getInReleaseAndNoPriceTransportSrcMsgIdList(userId, todayStartDate);
        Set<String> redisSet = stringRedisTemplate.opsForSet().members(TransportDynamicShowedTransportRedisKey + ":" + userId);
        if (redisSet != null && !redisSet.isEmpty()) {
            //过滤已经展示过的货源
            inReleaseAndNoPriceTransportSrcMsgIdList = inReleaseAndNoPriceTransportSrcMsgIdList.stream().filter(e -> !redisSet.contains(String.valueOf(e))).toList();
        }

        if (CollectionUtils.isEmpty(inReleaseAndNoPriceTransportSrcMsgIdList)) {
            TransportDynamicVO transportDynamicVO = new TransportDynamicVO();
            transportDynamicVO.setShowTransportDynamicTab(false);
            return transportDynamicVO;
        }

        int callConditionConfig = 2;
        int viewConditionConfig = 10;
        int firstPublishTimeConditionConfig = 45;
        String transportDynamicConditionConfig = tytConfigRemoteService.getStringValue("transport_Dynamic_Condition_Config", "2,10,45");
        if (StringUtils.isNotBlank(transportDynamicConditionConfig)) {
            String[] split = transportDynamicConditionConfig.split(",");
            if (split.length == 3) {
                callConditionConfig = Integer.parseInt(split[0]);
                viewConditionConfig = Integer.parseInt(split[1]);
                firstPublishTimeConditionConfig = Integer.parseInt(split[2]);
            }
        }

        List<Long> resultSrcMsgIdList = new ArrayList<>();

        //根据发布中货源ID查询符合>=2人联系的货源
        List<Long> recordCountParamBySrcMsgIdList = transportMainMapper.getCallCountParamBySrcMsgIdList(inReleaseAndNoPriceTransportSrcMsgIdList, callConditionConfig);
        if (CollectionUtils.isNotEmpty(recordCountParamBySrcMsgIdList)) {
            int numberOfElements1 = Math.min(recordCountParamBySrcMsgIdList.size(), 4);
            for (int i = 0; i < numberOfElements1; i++) {
                resultSrcMsgIdList.add(recordCountParamBySrcMsgIdList.get(i));
            }
        }

        if (resultSrcMsgIdList.size() < 4) {
            //根据发布中货源ID查询符合>=10人浏览的货源
            List<Long> uniqueList1 = inReleaseAndNoPriceTransportSrcMsgIdList.stream().filter(id -> !resultSrcMsgIdList.contains(id)).toList();
            if (!uniqueList1.isEmpty()) {
                List<Long> viewCountParamBySrcMsgIdList = transportMainMapper.getViewCountParamBySrcMsgIdList(uniqueList1, viewConditionConfig);
                if (CollectionUtils.isNotEmpty(viewCountParamBySrcMsgIdList)) {
                    int numberOfElements2 = Math.min(viewCountParamBySrcMsgIdList.size(), 4 - resultSrcMsgIdList.size());
                    for (int j = 0; j < numberOfElements2; j++) {
                        if (!resultSrcMsgIdList.contains(viewCountParamBySrcMsgIdList.get(j))) {
                            resultSrcMsgIdList.add(viewCountParamBySrcMsgIdList.get(j));
                        }
                    }
                }
            }

            if (resultSrcMsgIdList.size() < 4) {
                //根据发布中货源ID查询符合距离首发时间超过45分钟的货源（在查询货源时已满足该条件）
                LocalDateTime fortyFiveMinusEarlier = LocalDateTime.now().minusMinutes(firstPublishTimeConditionConfig);
                Date fortyFiveMinusEarlierDate = Date.from(fortyFiveMinusEarlier.atZone(ZoneId.systemDefault()).toInstant());
                List<Long> uniqueList2 = inReleaseAndNoPriceTransportSrcMsgIdList.stream().filter(id -> !resultSrcMsgIdList.contains(id)).toList();
                if (!uniqueList2.isEmpty()) {
                    List<Long> timeUpSrcMsgIdList = transportMainMapper.getInReleaseTransportAndCtimeEarlierSrcMsgIdList(uniqueList2, todayStartDate, fortyFiveMinusEarlierDate);
                    if (CollectionUtils.isNotEmpty(timeUpSrcMsgIdList)) {
                        int numberOfElements3 = Math.min(timeUpSrcMsgIdList.size(), 4 - resultSrcMsgIdList.size());
                        for (int k = 0; k < numberOfElements3; k++) {
                            if (!resultSrcMsgIdList.contains(timeUpSrcMsgIdList.get(k))) {
                                resultSrcMsgIdList.add(timeUpSrcMsgIdList.get(k));
                            }
                        }
                    }
                }
            }
        }

        if (resultSrcMsgIdList.isEmpty()) {
            TransportDynamicVO transportDynamicVO = new TransportDynamicVO();
            transportDynamicVO.setShowTransportDynamicTab(false);
            return transportDynamicVO;
        }

        TransportDynamicVO transportDynamicVO = new TransportDynamicVO();
        transportDynamicVO.setShowTransportDynamicTab(true);

        //构造货源信息
        List<TransportMainVO> transportList = new ArrayList<>();
        for (Long srcMsgId : resultSrcMsgIdList) {
            TransportMainDO byId = getById(srcMsgId);
            if (byId != null) {
                TransportMainVO transportMainVO = new TransportMainVO();
                BeanUtils.copyProperties(byId, transportMainVO);
                transportList.add(transportMainVO);
            }
        }
        transportDynamicVO.setTransportList(transportList);

        List<DynamicUserVO> dynamicUserVOList = new ArrayList<>();
        List<Long> userIds = transportMainMapper.getDynamicCarUserDataList(resultSrcMsgIdList);
        if (CollectionUtils.isNotEmpty(userIds)) {
            List<Long> userIdsLimit = userIds.stream().limit(30).toList();
            List<UserRpcVO> userRpcVOS = userRemoteService.getUserByIdList(userIdsLimit);
            if (CollectionUtils.isNotEmpty(userRpcVOS)) {
                for (UserRpcVO userRpcVO : userRpcVOS) {
                    DynamicUserVO dynamicUserVO = new DynamicUserVO();
                    dynamicUserVO.setId(userRpcVO.getId());
                    dynamicUserVO.setUserName(userRpcVO.getUserName());
                    dynamicUserVO.setHeadUrl(userRpcVO.getHeadUrl());
                    dynamicUserVOList.add(dynamicUserVO);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(dynamicUserVOList)) {
            transportDynamicVO.setInterestedPeopleNum(userIds.size());
            List<String> headUrlList = new ArrayList<>(dynamicUserVOList.stream().map(DynamicUserVO::getHeadUrl).filter(StringUtils::isNotBlank).limit(4).toList());
            if (dynamicUserVOList.size() > headUrlList.size() && headUrlList.size() < 4) {
                for (int i = 0; i < Math.min(dynamicUserVOList.size() - headUrlList.size(), 4 - headUrlList.size()); i++) {
                    headUrlList.add(" ");
                }
            }
            if (headUrlList.size() < 2) {
                int num = 2 - headUrlList.size();
                for (int i = 0; i < num; i++) {
                    headUrlList.add(" ");
                }
            }
            transportDynamicVO.setHeadUrlList(headUrlList);

            String showCarUserName = dynamicUserVOList.stream()
                    .map(DynamicUserVO::getUserName)
                    .filter(StringUtils::isNotBlank)
                    .findFirst()
                    .orElse(null);
            if (StringUtils.isNotBlank(showCarUserName)) {
                transportDynamicVO.setShowCarUserName(showCarUserName.charAt(0) + "先生");
            }
        }

        if (transportDynamicVO.getInterestedPeopleNum() == null || transportDynamicVO.getInterestedPeopleNum() == 0) {
            transportDynamicVO.setInterestedPeopleNum(ThreadLocalRandom.current().nextInt(1, 11));
        }


        if (CollectionUtils.isEmpty(transportDynamicVO.getHeadUrlList())) {
            List<String> headUrlList = new ArrayList<>();
            headUrlList.add(" ");
            headUrlList.add(" ");
            transportDynamicVO.setHeadUrlList(headUrlList);
        }

        if (StringUtils.isBlank(transportDynamicVO.getShowCarUserName())) {
            String[] characters = {"张", "王", "林"};
            Random random = new Random();
            int randomIndex = random.nextInt(characters.length);
            transportDynamicVO.setShowCarUserName(characters[randomIndex] + "先生");
        }

        //构造60分钟间隔缓存
        stringRedisTemplate.opsForValue().set(TransportDynamicRedisKey + ":" + userId, "1", 60, TimeUnit.MINUTES);

        //构造今日总弹窗次数缓存
        Date tomorrowDatStart = Date.from(LocalDate.now().atStartOfDay().plusDays(1).atZone(ZoneId.systemDefault()).toInstant());
        stringRedisTemplate.opsForValue().set(TransportDynamicTimesRedisKey + ":" + userId, String.valueOf(++times)
                , Duration.between(new Date().toInstant(), tomorrowDatStart.toInstant()).getSeconds(), TimeUnit.SECONDS);

        //构造已查看缓存
        List<String> resultSrcMsgIdStringList = resultSrcMsgIdList.stream()
                .map(String::valueOf)
                .toList();
        String[] resultSrcMsgIdArray = resultSrcMsgIdStringList.toArray(new String[0]);
        stringRedisTemplate.opsForSet().add(TransportDynamicShowedTransportRedisKey + ":" + userId, resultSrcMsgIdArray);
        stringRedisTemplate.expire(TransportDynamicShowedTransportRedisKey + ":" + userId, Duration.between(new Date().toInstant(), tomorrowDatStart.toInstant()).getSeconds(), TimeUnit.SECONDS);

        transportMainMapper.saveTransportDynamicShowLog(List.of(resultSrcMsgIdArray));

        return transportDynamicVO;

    }

    /**
     * 货源列表
     *
     * @param dto
     * @return
     */
    @Override
    public MyTransportVO getMyPublish(TransportListDTO dto) {
        MyTransportVO result = new MyTransportVO();
        List<MyTransportListVO> list = new ArrayList<>();
        Integer pageSize = dto.getPageSize();

        QueryMenuTypeEnum queryMenuTypeEnum = QueryMenuTypeEnum.getByType(dto.getQueryMenuType());
        switch (queryMenuTypeEnum) {
            case PUBLISHING:
                list = getPublishingList(dto, pageSize);
                fillCommonFields(list, dto);
                similarityTransportFidlds(list, dto);
                break;
            case CANCELED:
                list = getCanceledList(dto, pageSize);
                fillCommonFields(list, dto);
                break;
            case EXPIRED:
                list = getExpiredList(dto, pageSize);
                fillCommonFields(list, dto);
                break;
            case DONE:
                list = getDoneList(dto, pageSize);
                break;
            default:
                break;
        }
        result.setList(list);
        Integer publishingNum = getPublishingNum(dto.getUserId());
        result.setPublishingNum(publishingNum);
        return result;
    }

    private void similarityTransportFidlds(List<MyTransportListVO> list, TransportListDTO dto) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (MyTransportListVO vo : list) {
            TransportMainDO transportMainDO = this.getTransportMainForId(vo.getSrcMsgId());
            boolean hasPrice = TransportUtil.hasPrice(vo.getPrice());
            if (!hasPrice) {
                //如果货源无价，则获取是否存在有价相似货源
                vo.setSimilarityTransportHavePrice(getSimilarityTransportHavePriceCount(transportMainDO.getSimilarityCode()));
            }
        }
    }

    @Override
    public Boolean getSimilarityTransportHavePriceCount(String similarityCode) {
        Date endDate = new Date();
        Date startDate = DateUtil.beginOfDay(endDate).toJdkDate();
        int count = transportMapper.getSimilarityTransportHavePriceCount(similarityCode, startDate, endDate);
        return count > 0;
    }

    /**
     * 查询发布中货源数量
     *
     * @param userId
     * @return
     */
    private Integer getPublishingNum(Long userId) {
        return transportMapper.getPublishingNum(userId);
    }

    /**
     * 填充公共字段
     *
     * @param list
     */
    private void fillCommonFields(List<MyTransportListVO> list, TransportListDTO dto) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Long> srcMsgIdList = list.stream().map(MyTransportListVO::getSrcMsgId).collect(Collectors.toList());
        // 后台货源
        List<Long> backendSrcMsgIds = transportBackendMapper.selectBackendSrcMsgIds(srcMsgIdList);
        // 优车2.0是否展示 转电议 按钮，条件：1.用户在AB测；2.货源为优车2.0且为一口价；3.优车2.0路线勾选有价电议
        Integer excellent2Type = abTestRemoteService.getUserType("excellent2_allow_tele_negotiation", dto.getUserId());
        // 信用曝光按钮开关
        Integer onOff = tytConfigRemoteService.getIntValue("tyt:plat:config:publish:list:credit:retop:onoff", 0);
        // 开票货源
        List<TransportEnterpriseLogDO> invoiceList = transportEnterpriseLogMapper.getBySrcMsgIds(srcMsgIdList);
        // 用车类型
        List<TransportMainExtendDO> mainExtendDOS = transportMainExtendMapper.getBySrcMsgIds(srcMsgIdList);

        for (MyTransportListVO vo : list) {
            // 加价次数
            setAddMoneyNum(vo);
            // 是否是小程序货源
            setIsBackendTransport(backendSrcMsgIds, vo);
            // 处理距离
//            exchangeDistance(vo);
            // 优车2.0是否展示 转电议 按钮
            setIsAllowTeleNegotiation(excellent2Type, vo);
            // 检查信用曝光按钮开关状态
            if (onOff.equals(0)) {
                vo.setCreditRetop(0);
            }
            // 开票主体ID
            setInvoiceSubjectId(invoiceList, vo);
            // 用车类型 + 优惠价格
            setUseCarType(mainExtendDOS, vo);
            // 只有好货展示读秒货源标签，非好货把捂货过期时间置为空就隐藏了
            if (vo.getLabelJson() == null || !vo.getLabelJson().contains("\"iGBIResultData\":1")) {
                vo.setPriorityRecommendExpireTime(null);
            }
            // 处理小数
            decimalFiledProcess(vo);
        }
    }

    private void setIsBackendTransport(List<Long> backendSrcMsgIds, MyTransportListVO vo) {
        if (CollectionUtils.isNotEmpty(backendSrcMsgIds)) {
            Long backendSrcMsgId = backendSrcMsgIds.stream().filter(v -> v.equals(vo.getSrcMsgId())).findFirst().orElse(null);
            if (Objects.nonNull(backendSrcMsgId)) {
                vo.setIsBackendTransport(1);
            }
        }
    }

    private void setAddMoneyNum(MyTransportListVO vo) {
        String redisKey = FREIGHT_ADD_MONEY_NUM + "_" + vo.getUserId() + "_" + vo.getSrcMsgId();
        String addMoneyNum = redisUtil.getString(redisKey);
        vo.setAddMoneyNum(StringUtils.isNotEmpty(addMoneyNum) ? addMoneyNum : "0");
    }

    private void setUseCarType(List<TransportMainExtendDO> mainExtendDOS, MyTransportListVO vo) {
        if (CollectionUtils.isNotEmpty(mainExtendDOS)) {
            TransportMainExtendDO mainExtendDO = mainExtendDOS.stream().filter(v -> Objects.equals(v.getSrcMsgId(), vo.getSrcMsgId())).findFirst().orElse(null);
            if (Objects.nonNull(mainExtendDO)) {
                vo.setUseCarType(mainExtendDO.getUseCarType());
                vo.setPerkPrice(mainExtendDO.getPerkPrice());
            }
        }
    }

    private void setInvoiceSubjectId(List<TransportEnterpriseLogDO> invoiceList, MyTransportListVO vo) {
        if (Objects.equals(vo.getInvoiceTransport(), 1) && CollectionUtils.isNotEmpty(invoiceList)) {
            TransportEnterpriseLogDO logDO = invoiceList.stream().filter(v -> Objects.equals(vo.getSrcMsgId(), v.getSrcMsgId())).findFirst().orElse(null);
            if (Objects.nonNull(logDO) && Objects.nonNull(logDO.getInvoiceSubjectId())) {
                vo.setInvoiceSubjectId(logDO.getInvoiceSubjectId());
            }
        }
    }

    private void setIsAllowTeleNegotiation(Integer excellent2Type, MyTransportListVO vo) {
        if (Objects.equals(excellent2Type, 1)) {
            if (Objects.equals(vo.getExcellentGoodsTwo(), 2) && Objects.equals(vo.getPublishType(), 2)) {
                TytExcellentPriceConfigDO config = thPriceService.getConfig(buildCarryBean(vo));
                boolean isAllowTeleNegotiation = config != null && config.getPriceModel() != null && config.getPriceModel().contains("2");
                vo.setIsAllowTeleNegotiation(isAllowTeleNegotiation ? 1 : 0);
            }
        }
    }

    private TransportCarryReq buildCarryBean(MyTransportListVO vo) {
        TransportCarryReq transportCarryBean = new TransportCarryReq();
        transportCarryBean.setStartProvince(vo.getStartProvinc());
        transportCarryBean.setStartCity(vo.getStartCity());
        transportCarryBean.setStartArea(vo.getStartArea());
        transportCarryBean.setDestProvince(vo.getDestProvinc());
        transportCarryBean.setDestCity(vo.getDestCity());
        transportCarryBean.setDestArea(vo.getDestArea());
        transportCarryBean.setGoodsName(vo.getTaskContent());
        if (StringUtils.isNotBlank(vo.getWeight())) {
            transportCarryBean.setGoodsWeight(new BigDecimal(vo.getWeight()));
        }
        transportCarryBean.setGoodsLength(vo.getLength());
        transportCarryBean.setGoodsWide(vo.getWide());
        transportCarryBean.setGoodsHigh(vo.getHigh());
        transportCarryBean.setUserId(vo.getUserId());
//        BigDecimal distance = new BigDecimal("0");
//        if (StringUtils.isNotBlank(vo.getDistance())) {
//            distance = new BigDecimal(vo.getDistance());
//        }
        transportCarryBean.setDistance(vo.getDistance());
        transportCarryBean.setGoodTypeName(vo.getGoodTypeName());
        return transportCarryBean;
    }

    /**
     * 发布中货源列表
     *
     * @param dto
     * @return
     */
    private List<MyTransportListVO> getPublishingList(TransportListDTO dto, Integer pageSize) {
        List<MyTransportListVO> result = new ArrayList<>();
        List<TransportDO> list = transportMapper.getPublishingList(dto, pageSize);
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        List<Long> srcMsgIdList = list.stream().map(TransportDO::getSrcMsgId).collect(Collectors.toList());
        // 获取订单数量
        List<TsOrderCountVO> orderCountVOS = getOrderCount(srcMsgIdList);

        List<TransportDispatchViewDO> viewDOList = transportDispatchViewMapper.selectViewAndContactCount(srcMsgIdList);
        Map<Long, TransportDispatchViewDO> viewMap = viewDOList.stream().filter(t -> t.getSrcMsgId() != null)
                .collect(Collectors.toMap(TransportDispatchViewDO::getSrcMsgId, t -> t));
        // 赠送曝光卡的货源id
        List<Long> giveawayRecordList = exposureCardGiveawayService.getGiveawayRecord(srcMsgIdList);
        // 权益查询
        AuthPermissionRpcDTO authPermissionRpcDTO = new AuthPermissionRpcDTO();
        authPermissionRpcDTO.setServicePermissionEnum(ServicePermissionEnum.货源曝光权益);
        authPermissionRpcDTO.setUserId(dto.getUserId());
        AuthPermissionRpcVO authPermissionRpcVO = userPermissionRemoteService.checkAuthPermission(authPermissionRpcDTO);
        // 最大刷新次数
        Integer maxResendCounts = tytConfigRemoteService.getIntValue("tyt:plat:config:transport_top_max_count", 16);

        Integer havePriceTransportCount = 0;
        for (TransportDO mainDO : list) {
            MyTransportListVO vo = new MyTransportListVO();
            propertiesCopy(mainDO, vo);
            initOrderCount(orderCountVOS, vo);
            // 判断转一口价按钮是否展示
            initPriceButtonShow(vo);
            // 处理查看数量和联系数量
            initContactAndViewCount(viewMap.get(mainDO.getSrcMsgId()), vo);
            // 是否显示刷新按钮
            initGoodsRefresh(authPermissionRpcVO, maxResendCounts, vo);
            // 货源被出价次数
            initQuotedPriceTimes(vo);
            // 加价按钮样式处理
            havePriceTransportCount = initAddMoneyButtonStyle(vo, havePriceTransportCount);
            // 开票货源的指派车方信息
            initInvoiceTransportAssignCarTel(vo);
            // 送曝光卡标签，显示曝光任务
            initGiveawayExposureCardTask(vo, giveawayRecordList, viewMap.get(mainDO.getSrcMsgId()));
            result.add(vo);
        }
        return result;
    }

    /**
     * 处理小数点后的0
     *
     * @param vo
     */
    private void decimalFiledProcess(MyTransportListVO vo) {
        if (Objects.nonNull(vo.getInfoFee())) {
            vo.setInfoFee(vo.getInfoFee().setScale(0, RoundingMode.UP));
        }
        if (Objects.nonNull(vo.getTecServiceFee())) {
            vo.setTecServiceFee(vo.getTecServiceFee().setScale(0, RoundingMode.UP));
        }
    }

    /**
     * 填充订单数量字段
     *
     * @param orderCountVOS
     * @param vo
     */
    private void initOrderCount(List<TsOrderCountVO> orderCountVOS, MyTransportListVO vo) {
        if (CollectionUtils.isEmpty(orderCountVOS)) {
            return;
        }
        TsOrderCountVO countVO = orderCountVOS.stream().filter(v -> v.getSrcMsgId().equals(vo.getSrcMsgId())).findFirst().orElse(null);
        if (Objects.nonNull(countVO)) {
            vo.setOrderCount(countVO.getOrderCount());
        }
    }

    /**
     * 查询订单数量
     *
     * @param srcMsgIds
     */
    private List<TsOrderCountVO> getOrderCount(List<Long> srcMsgIds) {
        List<TsOrderCountVO> orders = new ArrayList<>();
        try {
            orders = ordersRemoteService.getOrderCountBySrcMsgIds(srcMsgIds);
        } catch (Exception e) {
            log.error("getOrderCountBySrcMsgIds查询订单数量失败:", e);
        }
        return orders;
    }

    /**
     * 字段拷贝
     *
     * @param transportDO
     * @param vo
     */
    public static void propertiesCopy(TransportDO transportDO, MyTransportListVO vo) {
        BeanUtils.copyProperties(transportDO, vo);
        vo.setPublishTime(transportDO.getCtime());
        vo.setTsId(transportDO.getId());
        vo.setGoodStatus(transportDO.getStatus());
        vo.setUserLevel(transportDO.getRankLevel());
//        vo.setStartLongitude(Objects.nonNull(transportDO.getStartLongitude()) ? String.valueOf(transportDO.getStartLongitude()) : "");
//        vo.setStartLatitude(Objects.nonNull(transportDO.getStartLatitude()) ? String.valueOf(transportDO.getStartLatitude()) : "");
//        vo.setDestLongitude(Objects.nonNull(transportDO.getDestLongitude()) ? String.valueOf(transportDO.getDestLongitude()) : "");
//        vo.setDestLatitude(Objects.nonNull(transportDO.getDestLatitude()) ? String.valueOf(transportDO.getDestLatitude()) : "");
    }

    /**
     * 开票货源的指派车方信息
     *
     * @param vo
     */
    private void initInvoiceTransportAssignCarTel(MyTransportListVO vo) {
        if (Objects.equals(vo.getInvoiceTransport(), 1)) {
            TransportEnterpriseLogDO logDO = transportEnterpriseLogMapper.getBySrcMsgId(vo.getSrcMsgId());
            if (Objects.nonNull(logDO)) {
                vo.setAssignCarTel(logDO.getAssignCarTel());
            }
        }
    }

    /**
     * 送曝光卡标签，显示曝光任务
     */
    private void initGiveawayExposureCardTask(MyTransportListVO vo, List<Long> giveawayRecordList, TransportDispatchViewDO viewDO) {
        boolean match = giveawayRecordList.contains(vo.getSrcMsgId());
        vo.setGiveawayExposureCard(match ? 1 : 0);
        // 命中送曝光卡规则 & 超过60分钟未成交，显示任务按钮
        vo.setShowTaskBtn(match && DateUtil.offsetMinute(vo.getReleaseTime(), 60).before(new Date()) ? 1 : 0);
        MyTransportListVO.ShowTaskInfo showTaskInfo = new MyTransportListVO.ShowTaskInfo();
        showTaskInfo.setTitle("刷新货源，提高曝光度");
        TransportLabelJson labelJson = JSON.parseObject(vo.getLabelJson(), TransportLabelJson.class);
        Integer expectViewCount = TransportUtil.expectViewCount(vo.getPrice(), labelJson == null ? null : labelJson.getGoodsModelScore(),
                viewDO == null ? 0 : viewDO.getViewCount());
        showTaskInfo.setContent("曝光后，预计增加" + expectViewCount + "名司机查看");
        vo.setShowTaskInfo(showTaskInfo);
    }

    /**
     * 处理加价按钮样式
     *
     * @param vo
     * @param havePriceTransportCount 有价货源数量
     */
    private Integer initAddMoneyButtonStyle(MyTransportListVO vo, Integer havePriceTransportCount) {
        if (StringUtils.isNotBlank(vo.getPrice()) && !Objects.equals(vo.getPrice(), "0")) {
            vo.setAddMoneyButtonStyle(1);

            // 只对列表前3个有价货源判断新的加价样式
            if (havePriceTransportCount < 3) {
                BigDecimal sameTransportAvgPrice = transportAfterOrderDataService.getSameTransportAvgPriceByDays(vo.getSrcMsgId(), 30);
                if (Objects.nonNull(sameTransportAvgPrice)) {
                    // 货源当前运费，低于近1个月相似货源平均成交价*80%
                    BigDecimal price = new BigDecimal(vo.getPrice());
                    if (price.compareTo(sameTransportAvgPrice.multiply(new BigDecimal("0.8"))) < 0) {
                        vo.setAddMoneyButtonStyle(3);
                        vo.setPriceDifference(sameTransportAvgPrice.subtract(price));
                        havePriceTransportCount = havePriceTransportCount + 1;
                        return havePriceTransportCount;
                    }
                }
            }

            Date lastTime = vo.getPublishTime();
            Date lastAddMoneyTime = backoutReasonMapper.getLastAddMoneyTime(vo.getSrcMsgId());
            if (Objects.nonNull(lastAddMoneyTime)) {
                lastTime = lastAddMoneyTime;
            }
            long lastTimeDiff = Duration.between(lastTime.toInstant(), new Date().toInstant()).toMinutes();
            if (Objects.equals(vo.getPublishType(), PublishTypeEnum.FIXED.getCode())) {
                // 一口价
                if (lastTimeDiff >= 30 || vo.getViewCount() > 10) {
                    vo.setAddMoneyButtonStyle(2);
                }
            } else {
                // 电议
                if (lastTimeDiff >= 30 || vo.getViewCount() > 10 || vo.getContactCount() == 0) {
                    vo.setAddMoneyButtonStyle(2);
                }
            }
            havePriceTransportCount = havePriceTransportCount + 1;
        }
        return havePriceTransportCount;
    }

    /**
     * 组装出价次数
     *
     * @param vo
     */
    private void initQuotedPriceTimes(MyTransportListVO vo) {
        Boolean checked = transportQuotedPriceService.checkTransportValidityV2(vo.getSrcMsgId(), false);
        if (!checked) {
            vo.setTransportQuotedPriceTimes(0);
        } else {
            Integer quotedPriceCount = transportQuotedPriceService.getTransportQuotedPriceCountBySrcMsgId(vo.getSrcMsgId());
            vo.setTransportQuotedPriceTimes(quotedPriceCount);
        }
    }

    /**
     * 组装货源刷新按钮
     *
     * @param authPermissionRpcVO
     * @param maxResendCounts
     * @param vo
     */
    @Override
    public void initGoodsRefresh(AuthPermissionRpcVO authPermissionRpcVO, Integer maxResendCounts, MyTransportListVO vo) {
        if (Objects.nonNull(authPermissionRpcVO) && authPermissionRpcVO.isUse()) {
            boolean canRefresh = checkTransportRefresh(maxResendCounts, vo.getResendCounts(), vo.getUserId(), vo.getPublishTime());
            vo.setShowGoodsRefresh(canRefresh ? 0 : 1);
        }
    }

    /**
     * 组装查看次数，联系人数
     */
    private void initContactAndViewCount(TransportDispatchViewDO viewDO, MyTransportListVO vo) {
        if (Objects.nonNull(viewDO)) {
            vo.setViewCount(viewDO.getViewCount());
            vo.setContactCount(viewDO.getContactCount());
        }
    }

    /**
     * 处理距离，库里存的12300，实际123km
     *
     * @param vo
     */
//    private void exchangeDistance(MyTransportListVO vo) {
//        if (StringUtils.isNotBlank(vo.getDistance())) {
//            vo.setDistance(CityUtil.toCoordStr(Integer.parseInt(vo.getDistance())));
//        }
//        vo.setStartLongitude(CityUtil.toMapPointStr(vo.getStartLongitude()));
//        vo.setStartLatitude(CityUtil.toMapPointStr(vo.getStartLatitude()));
//        vo.setDestLongitude(CityUtil.toMapPointStr(vo.getDestLongitude()));
//        vo.setDestLatitude(CityUtil.toMapPointStr(vo.getDestLatitude()));
//    }

    /**
     * 校验是否可以刷新
     *
     * @param maxResendCounts
     * @param resendCounts
     * @param userId
     * @param publishTime
     * @return
     */
    private boolean checkTransportRefresh(Integer maxResendCounts, Integer resendCounts, Long userId, Date publishTime) {
        long nowTimestamp = TimeUtil.getTimeStamp().getTime();
        long publishTimestamp = publishTime.getTime();

        //1. 校验刷新时间间隔
        long nowSecond = nowTimestamp / 1000L;
        long publishTimeSecond = publishTimestamp / 1000L;
        if (resendCounts > 0 && (nowSecond - publishTimeSecond) < -1) {
            return false;
        }

        //2. 校验最大刷新次数
        if (resendCounts >= maxResendCounts) {
            return false;
        }
        return true;
    }

    /**
     * 判断转一口价按钮是否展示(只有PC端在用)
     *
     * @param vo
     */
    private void initPriceButtonShow(MyTransportListVO vo) {
        int priceButtonShow = 0;

        if (Objects.equals(vo.getPublishType(), PublishTypeEnum.TELE.getCode())) {
            String startPoint = vo.getStartPoint();
            String startDetailAdd = vo.getStartDetailAdd();
            String destPoint = vo.getDestPoint();
            String price = vo.getPrice();
            BigDecimal infoFee = vo.getInfoFee();

            //出发地，详情，目的地，运费，信息费都不能为空
            if (StringUtils.isNotBlank(startPoint)
                    && StringUtils.isNotBlank(startDetailAdd)
                    && StringUtils.isNotBlank(destPoint)
                    && StringUtils.isNotBlank(price)
                    && infoFee != null
                    && infoFee.compareTo(BigDecimal.ZERO) > 0) {
                Integer orderCount = vo.getOrderCount();
                if (orderCount == null || orderCount <= 0) {
                    priceButtonShow = 1;
                }
            }
        }
        vo.setPriceButtonShow(priceButtonShow);
    }

    /**
     * 已撤销货源列表
     *
     * @param dto
     * @return
     */
    private List<MyTransportListVO> getCanceledList(TransportListDTO dto, Integer pageSize) {
        List<MyTransportListVO> result = new ArrayList<>();

        Integer queryDays = tytConfigRemoteService.getIntValue("infoFeeMyPublishDays", 30);
        Date startDate = TimeUtil.addDay(-queryDays);
        Date endDate = new Date();
        List<TransportMainDO> list = transportMainMapper.getCanceledList(dto, pageSize, startDate, endDate);
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        List<Long> srcMsgIdList = list.stream().map(TransportMainDO::getId).collect(Collectors.toList());
        List<TsOrderCountVO> orderCountVOS = getOrderCount(srcMsgIdList);

        // 货源撤销原因
        List<BackoutReasonDO> reasonDOS = backoutReasonMapper.selectALLReasonByMsgIds(srcMsgIdList);

        for (TransportMainDO mainDO : list) {
            MyTransportListVO vo = new MyTransportListVO();
            propertiesCopy(mainDO, vo);

            // 订单数量
            initOrderCount(orderCountVOS, vo);

            // 判断转一口价按钮是否展示
            initPriceButtonShow(vo);

            // 撤销原因
            BackoutReasonDO reasonDO = reasonDOS.stream().filter(v -> v.getSrcMsgId().equals(mainDO.getSrcMsgId())).findFirst().orElse(null);
            if (Objects.nonNull(reasonDO)) {
                vo.setBackoutReasonKey(reasonDO.getBackoutReasonKey());
            }
            result.add(vo);
        }
        return result;
    }

    private static void propertiesCopy(TransportMainDO mainDO, MyTransportListVO vo) {
        BeanUtils.copyProperties(mainDO, vo);
        vo.setPublishTime(mainDO.getCtime());
        vo.setCancelTime(mainDO.getMtime());
        vo.setTsId(mainDO.getId());
        vo.setSrcMsgId(mainDO.getId());
        vo.setGoodStatus(mainDO.getStatus());
        vo.setUserLevel(mainDO.getRankLevel());
//        vo.setStartLongitude(Objects.nonNull(mainDO.getStartLongitude()) ? String.valueOf(mainDO.getStartLongitude()) : "");
//        vo.setStartLatitude(Objects.nonNull(mainDO.getStartLatitude()) ? String.valueOf(mainDO.getStartLatitude()) : "");
//        vo.setDestLongitude(Objects.nonNull(mainDO.getDestLongitude()) ? String.valueOf(mainDO.getDestLongitude()) : "");
//        vo.setDestLatitude(Objects.nonNull(mainDO.getDestLatitude()) ? String.valueOf(mainDO.getDestLatitude()) : "");
    }

    /**
     * 已过期货源列表
     *
     * @param dto
     * @return
     */
    private List<MyTransportListVO> getExpiredList(TransportListDTO dto, Integer pageSize) {
        List<MyTransportListVO> result = new ArrayList<>();
        Integer queryDays = tytConfigRemoteService.getIntValue("infoFeeMyPublishDays", 30);
        Date startDate = TimeUtil.addDay(-queryDays);
        Date endDate = TimeUtil.getStartOfDay(new Date());
        List<TransportMainDO> list = transportMainMapper.getExpiredList(dto, pageSize, startDate, endDate);
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        List<Long> srcMsgIdList = list.stream().map(TransportMainDO::getId).collect(Collectors.toList());
        List<TsOrderCountVO> orderCountVOS = getOrderCount(srcMsgIdList);

        for (TransportMainDO mainDO : list) {
            MyTransportListVO vo = new MyTransportListVO();
            propertiesCopy(mainDO, vo);

            // 订单数量
            initOrderCount(orderCountVOS, vo);

            // 判断转一口价按钮是否展示
            initPriceButtonShow(vo);

            result.add(vo);
        }
        return result;
    }

    /**
     * 已成交货源列表
     *
     * @param dto
     * @return
     */
    private List<MyTransportListVO> getDoneList(TransportListDTO dto, Integer pageSize) {
        List<MyTransportListVO> result = new ArrayList<>();
        Integer queryDays = tytConfigRemoteService.getIntValue("infoFeeMyPublishDays", 30);
        Date startDate = TimeUtil.addDay(-queryDays);
        List<TransportDoneListDTO> list = transportDoneMapper.getDoneList(dto, pageSize, startDate);
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        List<Long> srcMsgIdList = list.stream().map(TransportDoneListDTO::getTsId).collect(Collectors.toList());
        List<Long> backendSrcMsgIds = transportBackendMapper.selectBackendSrcMsgIds(srcMsgIdList);
        // 开票货源
        List<TransportEnterpriseLogDO> invoiceList = transportEnterpriseLogMapper.getBySrcMsgIds(srcMsgIdList);
        List<TransportMainExtendDO> mainExtendDOS = transportMainExtendMapper.getBySrcMsgIds(srcMsgIdList);

        for (TransportDoneListDTO doneDto : list) {
            MyTransportListVO vo = new MyTransportListVO();
            propertiesCopy(doneDto, vo);

            // 距离及经纬度处理
//            exchangeDistance(vo);

            String locationCache = redisUtil.getString(CACHE_EXPIRE_LOCATION_APPLY + doneDto.getTsId());
            if (StringUtils.isNotBlank(locationCache) && Objects.equals(doneDto.getIsAllowLocation(), 2)) {
                // 不允许定位，设置为申请中
                vo.setIsAllowLocation(3);
            }
            // 是否是小程序货源
            setIsBackendTransport(backendSrcMsgIds, vo);
            // 开票主体ID
            setInvoiceSubjectId(invoiceList, vo);
            // 处理小数
            decimalFiledProcess(vo);
            // 用车类型
            setUseCarType(mainExtendDOS, vo);
            result.add(vo);
        }
        return result;
    }

    private static void propertiesCopy(TransportDoneListDTO doneDto, MyTransportListVO vo) {
        BeanUtils.copyProperties(doneDto, vo);
        vo.setStartLongitude(CityUtil.toMapPointBigDecimal(doneDto.getStartLongitude()));
        vo.setStartLatitude(CityUtil.toMapPointBigDecimal(doneDto.getStartLatitude()));
        vo.setDestLongitude(CityUtil.toMapPointBigDecimal(doneDto.getDestLongitude()));
        vo.setDestLatitude(CityUtil.toMapPointBigDecimal(doneDto.getDestLatitude()));
    }

    @Override
    public List<TransportMainExtendDO> getExtendList(List<Long> srcMsgIds) {
        if (CollectionUtils.isEmpty(srcMsgIds)) {
            return List.of();
        }
        return transportMainExtendMapper.getBySrcMsgIds(srcMsgIds);
    }

    @Override
    public CheckIsNeedFreeTecServiceFeeVO checkIsNeedFreeTecSericeFeeByCarUser(Long userId, Long srcMsgId) {
        log.info("判断是否符合车方直接免佣条件 userId:{},srcMsgId:{}", userId, srcMsgId);
        CheckIsNeedFreeTecServiceFeeVO result = new CheckIsNeedFreeTecServiceFeeVO();
        result.setNeedFreeTec(false);
        if (userId == null || srcMsgId == null) {
            return result;
        }
        UserRpcVO user = null;
        try {
            user = userRemoteService.getUser(userId);
        } catch (Exception e) {
            return result;
        }

        if (user == null || user.getCtime() == null || user.getId() == null) {
            return result;
        }

        // 创建2024年7月1日00:00:00的LocalDateTime
        LocalDateTime julyFirst2024 = LocalDateTime.of(2024, 7, 1, 0, 0, 0);

        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 将ctime转换为LocalDateTime
        LocalDateTime ctimeLocalDateTime = user.getCtime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

        // 判断ctime是否在2024年7月1日及之后
        boolean isAfterJulyFirst2024 = !ctimeLocalDateTime.isBefore(julyFirst2024);

        // 判断ctime距离今天是否小于30天
        long daysBetween = ChronoUnit.DAYS.between(ctimeLocalDateTime, now);
        boolean isWithinLast30Days = daysBetween < 30;

        boolean isPerformanceNum = false;

        // 判断货主是否履约过
        UserPerformanceNumDTO userPerformanceNumDTO = new UserPerformanceNumDTO();
        userPerformanceNumDTO.setUserId(user.getId());
        // 1:车主  2：货主
        userPerformanceNumDTO.setUserType(1);
        userPerformanceNumDTO.setRiskOrderFlag(1);
        Integer userPerformanceNum = InfoFeeRemoteService.getUserPerformanceNum(userPerformanceNumDTO);
        log.info("获取车方履约单数量 userId:{}, result:{}", userId, userPerformanceNum);
        //用户有无履约超过3次，返回是，否则返回否
        if (userPerformanceNum != null && userPerformanceNum >= 0 && userPerformanceNum < 3) {
            isPerformanceNum = true;
        }

        if (isAfterJulyFirst2024 && isWithinLast30Days && isPerformanceNum) {
            result.setNeedFreeTec(true);
            result.setWord("新客权益 免收费用");
            tytFreeTecServiceFeeCarUserLogMapper.replaceIntoByCarUserIdAndSrcMsgId(userId, srcMsgId);
        }
        log.info("是否符合车方直接免佣条件判断结果 userId:{},srcMsgId:{},result:{}", userId, srcMsgId, JSONObject.toJSONString(result));
        return result;
    }

    /**
     * 根据ID查询货源信息
     *
     * @param srcMsgId
     * @return
     */
    @Override
    public TransportMainDO getTransportMainForId(Long srcMsgId) {
        return transportMainMapper.selectById(srcMsgId);
    }

    @Override
    public CheckIsNeedFreeTecServiceFeeVO checkIsNeedFreeTecSericeFeeByTransport(Long transportUserId, String startCity, boolean isGoodCarPriceTransport) {
        log.info("判断是否符合发货直接免佣条件 transportUserId:{},startCity:{},isGoodCarPriceTransport:{}", transportUserId, startCity, isGoodCarPriceTransport);
        CheckIsNeedFreeTecServiceFeeVO checkIsNeedFreeTecServiceFeeVO = new CheckIsNeedFreeTecServiceFeeVO();
        checkIsNeedFreeTecServiceFeeVO.setNeedFreeTec(false);
        ArrayList<Integer> freeTecTypeList = new ArrayList<>();
        checkIsNeedFreeTecServiceFeeVO.setNeedFreeTecTypeList(freeTecTypeList);

        //新用户首履免佣
        boolean isPerformanceNum = false;
        TransportMainDO transportMainDO = getFirstTransport(transportUserId);
        if (transportMainDO != null && transportMainDO.getCtime() != null) {
            Date ctime = transportMainDO.getCtime();
            if (!DateUtils.addDays(ctime, 30).before(new Date())) {
                //当前发货时间距首次发货时间不足30天
                UserPerformanceNumDTO userPerformanceNumDTO = new UserPerformanceNumDTO();
                userPerformanceNumDTO.setUserId(transportUserId);
                // 1:车主  2：货主
                userPerformanceNumDTO.setUserType(2);
                userPerformanceNumDTO.setRiskOrderFlag(1);
                Integer userPerformanceNum = InfoFeeRemoteService.getUserPerformanceNum(userPerformanceNumDTO);
                //用户有无履约，返回是，否则返回否
                if (userPerformanceNum != null && userPerformanceNum == 0) {
                    isPerformanceNum = true;
                }
            }
        } else {
            isPerformanceNum = true;
        }

        //S城市免佣
        boolean cityFreeTecServiceFee = false;
        String freeTecServiceFeeCity = tytConfigRemoteService.getStringValue("free_tec_service_fee_city");
        if (StringUtils.isNotBlank(freeTecServiceFeeCity) && StringUtils.isNotBlank(startCity) && freeTecServiceFeeCity.contains(startCity)) {
            cityFreeTecServiceFee = true;
        }

        //优车2.0首履免佣
        boolean goodCarPriceTransportFreeTecServiceFee = false;
        //要发优车2.0的货
        //如果该货主是个人货主或者企业货主才进行接下来的逻辑
        DwsNewIdentiwoDataRpcVO identityUser = null;
        try {
            identityUser = userRemoteService.getDwsNewIdentiwoDataByUserId(transportUserId);
        } catch (Exception e) {
            log.error("判断是否符合发货直接免佣条件异常，获取用户身份信息失败，transportUserId:{}", transportUserId, e);
        }
        if (isGoodCarPriceTransport && identityUser != null && DwsIdentityTypeEnum.isCargoOwner(identityUser.getType())) {
            //获取近半年履约单的货源ID
            UserPerformanceNumDTO userPerformanceNumDTO = new UserPerformanceNumDTO();
            userPerformanceNumDTO.setUserId(transportUserId);
            // 1:车主  2：货主
            userPerformanceNumDTO.setUserType(2);
            userPerformanceNumDTO.setRiskOrderFlag(1);
            List<Long> srcMsgIds = InfoFeeRemoteService.getUserPerformanceTsIds(userPerformanceNumDTO);
            if (CollectionUtils.isNotEmpty(srcMsgIds)) {
                //获取这些货源ID中属于优车定价的货源
                Integer count = transportMainMapper.IsHaveGoodCarPriceTransportBySrcMsgIdList(srcMsgIds);
                log.info("获取货方近半年履约单货源ID中优车2.0货源总数 srcMsgIds:{}, result:{}", JSONObject.toJSONString(srcMsgIds), count);
                if (count != null && count == 0) {
                    goodCarPriceTransportFreeTecServiceFee = true;
                }
            } else {
                goodCarPriceTransportFreeTecServiceFee = true;
            }
        }

        if (isPerformanceNum) {
            checkIsNeedFreeTecServiceFeeVO.setNeedFreeTec(true);
            freeTecTypeList.add(1);
        }

        if (cityFreeTecServiceFee) {
            checkIsNeedFreeTecServiceFeeVO.setNeedFreeTec(true);
            freeTecTypeList.add(3);
        }

        if (goodCarPriceTransportFreeTecServiceFee) {
            checkIsNeedFreeTecServiceFeeVO.setNeedFreeTec(true);
            freeTecTypeList.add(4);
        }

        log.info("是否符合发货直接免佣条件判断结果 transportUserId:{},startCity:{},isGoodCarPriceTransport:{}, result:{}", transportUserId, startCity, isGoodCarPriceTransport, JSONObject.toJSONString(checkIsNeedFreeTecServiceFeeVO));
        return checkIsNeedFreeTecServiceFeeVO;
    }

    @Override
    public Long getLastTransportInvoiceSubjectId(Long userId) {
        Long chooseInvoiceSubjectId = null;
        String serviceProviderCode = null;
        TransportMainDO transportMainDO = transportMainMapper.getLastTransportByTransportUserId(userId);
        if (transportMainDO != null && transportMainDO.getSrcMsgId() != null && transportMainDO.getInvoiceTransport() != null && transportMainDO.getInvoiceTransport() == 1) {
            TransportEnterpriseLogDO enterpriseLogDO = transportEnterpriseLogMapper.getBySrcMsgId(transportMainDO.getSrcMsgId());
            if (enterpriseLogDO != null) {
                //找到上一票新发的货的开票主体ID和开票服务商code
                chooseInvoiceSubjectId = enterpriseLogDO.getInvoiceSubjectId();
                serviceProviderCode = enterpriseLogDO.getServiceProviderCode();
            }
        }

        if (chooseInvoiceSubjectId != null) {
            final Long chooseInvoiceSubjectIdFinal = chooseInvoiceSubjectId;
            final String serviceProviderCodeFinal = serviceProviderCode;
            //判断该用户这个主体是否可用，如果可用直接返回否则返回空
            WebResult<List<InvoiceDominantVo>> invoiceDominantListWebResult = thirdEnterpriseRemoteService.getInvoiceDominantList(userId);
            if (invoiceDominantListWebResult.getCode().equals(WebResult.success().getCode())) {
                List<InvoiceDominantVo> invoiceDominantList = invoiceDominantListWebResult.getData();
                if (CollectionUtils.isNotEmpty(invoiceDominantList)) {
                    // 如果chooseInvoiceSubjectId在invoiceDominantList里面存在一样的dominantId，则直接返回chooseInvoiceSubjectId
                    // 否则就要invoiceDominantList里面存在一样的serviceProviderCode的第0位，如果存在返回那个的dominantId，否则直接返回整个invoiceDominantList的第0位的dominantId
                    InvoiceDominantVo invoiceDominantVo = invoiceDominantList.stream().filter(v -> Objects.equals(v.getDominantId(), chooseInvoiceSubjectIdFinal)).findFirst().orElse(null);
                    if (invoiceDominantVo != null) {
                        return chooseInvoiceSubjectId;
                    } else {
                        invoiceDominantVo = invoiceDominantList.stream().filter(v -> Objects.equals(v.getServiceProviderCode(), serviceProviderCodeFinal)).findFirst().orElse(null);
                        if (invoiceDominantVo != null) {
                            return invoiceDominantVo.getDominantId();
                        } else {
                            return invoiceDominantList.get(0).getDominantId();
                        }
                    }
                }
            }
        }


        return null;
    }

    /**
     * 获取上一次成交的相同货源信息
     */
    @Override
    public TransportMainDO getLastSameDealTransport(TransportMainDO transportMainDO) {
        return transportMainMapper.getLastSameDealTransport(transportMainDO);
    }

    /**
     * 生成相似货源code
     */
    @Override
    public String genSimilarityCode(TransportMainDO transportMainDO) {

        // 如果有matchItemId，则获取matchItemId的brand、goodTypeName、type
        if (transportMainDO.getMatchItemId() != null) {
            MachineTypeBrandNewDO machineTypeBrandNew = machineTypeBrandNewService.getById(transportMainDO.getMatchItemId().longValue());
            if (machineTypeBrandNew != null) {
                transportMainDO.setBrand(StringUtils.defaultIfBlank(machineTypeBrandNew.getBrand(), ""));
                transportMainDO.setGoodTypeName(StringUtils.defaultIfBlank(machineTypeBrandNew.getSecondClass(), ""));
                transportMainDO.setType(StringUtils.defaultIfBlank(machineTypeBrandNew.getTopType(), ""));
            }
        }

        String todayDate = TimeUtil.formatDate(new Date());
        String startProvinc = StringUtils.defaultIfBlank(transportMainDO.getStartProvinc(), "")
                .replace("市", "").replace("省", "");
        String startCity = StringUtils.defaultIfBlank(transportMainDO.getStartCity(), "");
        String startArea = StringUtils.defaultIfBlank(Objects.equals("全市", transportMainDO.getStartArea()) ? "" : transportMainDO.getStartArea(), "");
        String destProvinc = StringUtils.defaultIfBlank(transportMainDO.getDestProvinc(), "")
                .replace("市", "").replace("省", "");
        String destCity = StringUtils.defaultIfBlank(transportMainDO.getDestCity(), "");
        String destArea = StringUtils.defaultIfBlank(Objects.equals("全市", transportMainDO.getDestArea()) ? "" : transportMainDO.getDestArea(), "");
        String brand = StringUtils.defaultIfBlank(transportMainDO.getBrand(), "");
        String goodTypeName = StringUtils.defaultIfBlank(transportMainDO.getGoodTypeName(), "");
        String type = StringUtils.defaultIfBlank(transportMainDO.getType(), "");
        String taskContent = StringUtils.defaultIfBlank(transportMainDO.getTaskContent(), "");

        //规则：今天的日期 + 出发地省 + 出发地市 + 出发地区 + 目的地省 + 目的地市 + 目的地区 + 重量 + 货名
        String similarityConnect = todayDate + startProvinc + startCity + startArea + destProvinc + destCity + destArea;
        if (StringUtils.isBlank(brand) && StringUtils.isBlank(goodTypeName) && StringUtils.isBlank(type)) {
            similarityConnect = similarityConnect + StringUtils.substring(taskContent, 0, 10);
        } else {
            similarityConnect = similarityConnect + brand + goodTypeName + type;
        }


        // 先判断抽佣货源的开关，走抽佣货源的逻辑
        Integer similaritySwitch = tytConfigRemoteService.getIntValue("commission_similarity_switch", 0);
        // 0：折叠  1：不折叠
        if (Objects.equals(1, similaritySwitch)) {
            log.info("生成相似货源走抽佣货源逻辑，srcMsgId: {}", transportMainDO.getSrcMsgId());
            if (StringUtils.isNotBlank(transportMainDO.getLabelJson())) {
                TransportLabelJson transportLabelJson = JSON.parseObject(transportMainDO.getLabelJson(), TransportLabelJson.class);
                if (Objects.equals(transportLabelJson.getCommissionTransport(), 1)) {
                    similarityConnect += transportMainDO.getUserId();
                }
            }
        }
        // 优车货源如果在ab测，不折叠
        if (Objects.equals(1, transportMainDO.getExcellentGoods())) {
            log.info("生成相似货源走优车货源逻辑，srcMsgId: {}", transportMainDO.getSrcMsgId());
            similarityConnect += transportMainDO.getUserId();
        }
        log.info("生成相似货源code开始，srcMsgId:{}, similarity_connect: {}", transportMainDO.getSrcMsgId(), similarityConnect);
        //相似code
        String similarityCode = MD5Util.GetMD5Code(similarityConnect);
        // 如果管理后台手动修改过similarityCode，则需要拼接后缀
        if (transportMainDO.getSrcMsgId() != null && redisUtil.get(RedisKeyConstant.GOODS_SIMILARITY_CHANGE + transportMainDO.getSrcMsgId()) != null) {
            similarityCode += "_" + (int) (Math.random() * 1000);
        }
        log.info("生成相似货源code结束，srcMsgId:{}, similarityCode: {}", transportMainDO.getSrcMsgId(), similarityCode);
        return similarityCode;

    }

    public static void main(String[] args) {
        String s = "2025-02-08北京北京市海淀区山西省大同市平城区挖掘机200";
        System.out.println(MD5Util.GetMD5Code(s));
    }

    /**
     * 统计相似货源数
     *
     * @param similarityCode
     */
    @Override
    public int countSimilarityGoods(String similarityCode, Long srcMsgId) {
        LambdaQueryWrapper<TransportMainDO> lambdaQueryWrapper = new LambdaQueryWrapper<TransportMainDO>()
                .eq(TransportMainDO::getSimilarityCode, similarityCode)
                .eq(TransportMainDO::getStatus, 1);
        if (srcMsgId != null) {
            lambdaQueryWrapper.ne(TransportMainDO::getSrcMsgId, srcMsgId);
        }
        return baseMapper.selectCount(lambdaQueryWrapper).intValue();
    }

    /**
     * 查询货源相似货源数量（发布中状态）
     *
     * @param srcMsgId
     * @return
     */
    @Override
    public int countSimilarityGoods(Long srcMsgId) {
        TransportMainDO main = getTransportMainForId(srcMsgId);
        if (Objects.isNull(main)) {
            return 0;
        }
        return transportMainMapper.countSimilarityGoods(srcMsgId, main.getSimilarityCode());
    }

    /**
     * 查询同步线货源数量（发布中状态）
     *
     * @param srcMsgId
     * @return
     */
    @Override
    public int countSameRouteGoods(Long srcMsgId) {
        TransportMainDO main = getTransportMainForId(srcMsgId);
        if (Objects.isNull(main)) {
            return 0;
        }
        return transportMainMapper.countSameRouteGoods(srcMsgId, main.getStartCity(), main.getDestCity());
    }

    @Override
    public List<String> selectOfPublishType(Long userId, Date publishTime) {
        return transportMainMapper.selectOfPublishType(userId, publishTime);
    }

    @Override
    public void initRecordGoodsTransactionInfo(TransportMainDO transportMainForId) {
        String distance = null;
        if (transportMainForId.getDistance() != null) {
            distance = transportMainForId.getDistance().toString();
        }

        transportMainMapper.initRecordGoodsTransactionInfo(transportMainForId.getSrcMsgId(), transportMainForId.getUserId(), transportMainForId.getTaskContent(), transportMainForId.getGoodTypeName()
                , distance, transportMainForId.getStartCity(), transportMainForId.getDestCity(), transportMainForId.getWeight()
                , transportMainForId.getPrice());
    }

    @Override
    public Date hasUserTransportLast30Day(Long userId) {
        return transportMainMapper.hasUserTransportLast30Day(userId);
    }

    @Override
    public List<TransportMainDO> getUserSomeDayTransportData(Long userId, String lastPublishDay) {
        return transportMainMapper.getUserSomeDayTransportData(userId, lastPublishDay);
    }

    @Override
    public Integer getPublishCountByUserId(Long userId, Date startTime, Date endTime) {
        return transportMainMapper.getPublishCountByUserId(userId, startTime, endTime);
    }

    @Override
    public List<TransportMainDO> getUserCommissionGoods(Long userId) {
        return transportMainMapper.getUserCommissionGoods(userId);
    }

    @Override
    public SimilarityTransportTurnoverRatioVO getSimilarityTransportTurnoverRatio(TransportSimilarityDTO similarityDTO) {
        Date now = new Date();
        Date endDate = DateUtil.beginOfDay(DateUtil.offsetDay(now, 1)).toJdkDate();
        Date startDate = DateUtil.beginOfDay(DateUtil.offsetDay(now, -7)).toJdkDate();

        BigDecimal bigDecimal2 = new BigDecimal(2);
        BigDecimal bigDecimal10 = new BigDecimal(10);
        BigDecimal bigDecimal40 = new BigDecimal(40);
        BigDecimal bigDecimal80 = new BigDecimal(80);
        BigDecimal bigDecimal95 = new BigDecimal(95);
        BigDecimal bigDecimal100 = new BigDecimal(100);

        SameCityTransportCountVO lowSameCityTransportCount = new SameCityTransportCountVO();
        SameCityTransportCountVO highSameCityTransportCount = new SameCityTransportCountVO();
        SameCityTransportCountVO highestSameCityTransportCount = new SameCityTransportCountVO();
        if (StringUtils.isNotBlank(similarityDTO.getStartCity()) && StringUtils.isNotBlank(similarityDTO.getDestCity())) {
            Integer sameCityTransportAllCount = transportMainMapper.getSameCityTransportAllCount(similarityDTO.getStartCity(), similarityDTO.getDestCity(), 1, startDate, endDate);
            Integer sameCityTransportBargainCont = transportMainMapper.getSameCityTransportBargainCont(similarityDTO.getStartCity(), similarityDTO.getDestCity(), 1, startDate, endDate);
            lowSameCityTransportCount.setAllCount(sameCityTransportAllCount);
            lowSameCityTransportCount.setBargainCont(sameCityTransportBargainCont);

            Integer sameCityTransportAllCount1 = transportMainMapper.getSameCityTransportAllCount(similarityDTO.getStartCity(), similarityDTO.getDestCity(), 2, startDate, endDate);
            Integer sameCityTransportBargainCont1 = transportMainMapper.getSameCityTransportBargainCont(similarityDTO.getStartCity(), similarityDTO.getDestCity(), 2, startDate, endDate);
            highSameCityTransportCount.setAllCount(sameCityTransportAllCount1);
            highSameCityTransportCount.setBargainCont(sameCityTransportBargainCont1);

            Integer sameCityTransportAllCount2 = transportMainMapper.getSameCityTransportAllCount(similarityDTO.getStartCity(), similarityDTO.getDestCity(), 3, startDate, endDate);
            Integer sameCityTransportBargainCont2 = transportMainMapper.getSameCityTransportBargainCont(similarityDTO.getStartCity(), similarityDTO.getDestCity(), 3, startDate, endDate);
            highestSameCityTransportCount.setAllCount(sameCityTransportAllCount2);
            highestSameCityTransportCount.setBargainCont(sameCityTransportBargainCont2);
        }
        SimilarityTransportTurnoverRatioVO similarityTransportTurnoverRatioVO = new SimilarityTransportTurnoverRatioVO();
        similarityTransportTurnoverRatioVO.setLowRatio(BigDecimal.ZERO);
        similarityTransportTurnoverRatioVO.setHighRatio(BigDecimal.ZERO);
        similarityTransportTurnoverRatioVO.setHighestRatio(BigDecimal.ZERO);

        if (lowSameCityTransportCount.getAllCount() != null && lowSameCityTransportCount.getBargainCont() != null) {
            BigDecimal lowRatio = new BigDecimal(0);
            if (lowSameCityTransportCount.getAllCount() > 0 && lowSameCityTransportCount.getBargainCont() > 0) {
                lowRatio = new BigDecimal(lowSameCityTransportCount.getBargainCont()).divide(new BigDecimal(lowSameCityTransportCount.getAllCount()), 2, RoundingMode.UP);
            }
            lowRatio = lowRatio.multiply(bigDecimal100).add(bigDecimal10);
            if (lowRatio.compareTo(bigDecimal40) > 0) {
                lowRatio = bigDecimal40;
            }
            similarityTransportTurnoverRatioVO.setLowRatio(lowRatio);
        }

        if (highSameCityTransportCount.getAllCount() != null && highSameCityTransportCount.getBargainCont() != null) {
            BigDecimal highRatio = new BigDecimal(0);
            if (highSameCityTransportCount.getAllCount() > 0 && highSameCityTransportCount.getBargainCont() > 0) {
                highRatio = new BigDecimal(highSameCityTransportCount.getBargainCont()).divide(new BigDecimal(highSameCityTransportCount.getAllCount()), 2, RoundingMode.UP);
            }
            highRatio = highRatio.multiply(bigDecimal100).add(bigDecimal10).multiply(bigDecimal2);
            if (highRatio.compareTo(similarityTransportTurnoverRatioVO.getLowRatio()) < 0) {
                highRatio = similarityTransportTurnoverRatioVO.getLowRatio().multiply(new BigDecimal("1.6"));
            }
            if (highRatio.compareTo(bigDecimal80) > 0) {
                highRatio = bigDecimal80;
            }
            similarityTransportTurnoverRatioVO.setHighRatio(highRatio);
        }

        if (highestSameCityTransportCount.getAllCount() != null && highestSameCityTransportCount.getBargainCont() != null) {
            BigDecimal highestRatio = new BigDecimal(0);
            if (highestSameCityTransportCount.getAllCount() > 0 && highestSameCityTransportCount.getBargainCont() > 0) {
                highestRatio = new BigDecimal(highestSameCityTransportCount.getBargainCont()).divide(new BigDecimal(highestSameCityTransportCount.getAllCount()), 2, RoundingMode.UP);
            }
            highestRatio = highestRatio.multiply(bigDecimal100).add(bigDecimal10).multiply(bigDecimal2);
            if (highestRatio.compareTo(similarityTransportTurnoverRatioVO.getHighRatio()) < 0) {
                highestRatio = similarityTransportTurnoverRatioVO.getHighRatio().multiply(new BigDecimal("1.2"));
            }
            if (highestRatio.compareTo(bigDecimal95) > 0) {
                highestRatio = bigDecimal95;
            }
            similarityTransportTurnoverRatioVO.setHighestRatio(highestRatio);
        }
        return similarityTransportTurnoverRatioVO;
    }

    /**
     * 校验重货
     *
     * @param transportMainDO
     */
    @Override
    public boolean checkDuplicateTransport(String newHashCode, TransportMainDO transportMainDO) {
        String dateStr = DateUtil.beginOfDay(new Date()).toDateStr();
        Long userId = transportMainDO.getUserId();
        String telsStr = StringUtils.joinWith(",", transportMainDO.getTel(), transportMainDO.getTel3(), transportMainDO.getTel4());
        int count = transportMainMapper.countDuplicateTransport(newHashCode, dateStr, userId, telsStr);
        return count > 0;
    }

    @Override
    public void saveTransportCreditExposure(Long srcMsgId, Integer creditRetop) {
        transportMainMapper.saveTransportCreditExposure(srcMsgId, creditRetop);
    }

    /**
     * 获取相似货源首条货源
     *
     * @param similarityCode
     */
    @Override
    public TransportMainDO getFirstSimilarityGoods(String similarityCode) {
        return transportMainMapper.getFirstSimilarityGoods(similarityCode);
    }

    @Override
    public Integer selectDistanceBySrcMsgId(Long srcMsgId) {
        return transportMainMapper.selectDistanceBySrcMsgId(srcMsgId);
    }

    private TransportMainDO getFirstTransport(Long transportUserId) {
        return transportMainMapper.getFirstTransport(transportUserId);
    }

    @Override
    public void noDisplay(Long srcMsgId) {
        transportMainMapper.noDisplay(srcMsgId);
    }

    @Override
    public TransportLabelJson getTransportLabelJson(Long srcMsgId) {
        TransportLabelJson labelJson = new TransportLabelJson();
        TransportMainDO main = this.getTransportMainForId(srcMsgId);
        if (Objects.isNull(main)) {
            return labelJson;
        }
        String labelJsonText = main.getLabelJson();
        if (StringUtils.isNotBlank(labelJsonText)) {
            labelJson = JSON.parseObject(labelJsonText, TransportLabelJson.class);
        }
        if (labelJson == null) {
            labelJson = new TransportLabelJson();
        }
        return labelJson;
    }

    /**
     * 校验用户是否是货主
     *
     * @param userId
     * @param srcMsgId
     * @return
     */
    @Override
    public boolean checkUserIsTransportOwner(Long userId, Long srcMsgId) {
        if (userId == null || srcMsgId == null) {
            return false;
        }
        TransportMainDO transportMain = this.getTransportMainForId(srcMsgId);
        if (transportMain == null || transportMain.getUserId() == null) {
            return false;
        }
        return Objects.equals(userId, transportMain.getUserId());
    }

    @Override
    public void invalidTransport(Long srcMsgId) {
        transportMainMapper.invalidTransport(srcMsgId);
    }

    /**
     * 校验是否是参与现金奖活动货源
     * a. 有抽佣标签的货源且技术服务费>0
     * b. 经分口径下的直客或物流公司的发布的前3个货源
     * c. 经分口径下的直客的货源，距离首发时间超过2小时还未成交的货源
     *
     * @param transport
     */
    @Override
    public boolean isCashPrizeActivityTransport(TransportMainDO transport) {
        boolean isCashPrizeActivityGoods = false;
        try {
            log.info("校验是否是参与现金奖活动货源，货源id:{}", transport.getId());
            TransportLabelJson transportLabelJson = JSON.parseObject(transport.getLabelJson(), TransportLabelJson.class);
            if (transportLabelJson == null) {
                transportLabelJson = new TransportLabelJson();
            }

            // 抽佣货源&服务费>0
            if (Objects.equals(transportLabelJson.getCommissionTransport(), 1)
                    && transport.getTecServiceFee() != null && transport.getTecServiceFee().compareTo(BigDecimal.ZERO) > 0) {
                log.info("校验是否是参与现金奖活动货源，货源id:{}，抽佣且技术服务费>0", transport.getId());
                isCashPrizeActivityGoods = true;
            } else {
                DwsNewIdentiwoDataRpcVO userIdentity = userRemoteService.getDwsNewIdentiwoDataByUserId(transport.getUserId());
                if (userIdentity != null) {
                    // 直客2小时内未成交
                    if (DwsIdentityTypeEnum.isCargoOwner(userIdentity.getType())) {
                        if (DateUtils.addHours(transport.getReleaseTime(), 2).before(new Date())) {
                            log.info("校验是否是参与现金奖活动货源，货源id:{}，直客2小时内未成交", transport.getId());
                            isCashPrizeActivityGoods = true;
                        }
                        // 直客或物流公司的发布的前3个货源
                    } else if ((DwsIdentityTypeEnum.isCargoOwner(userIdentity.getType())
                            || DwsIdentityTypeEnum.LOGISTICS_COMPANIES.getCode().equals(userIdentity.getType()))) {
                        // 未首履过（没有剔除风险单）的直客（货主）发布的货源
                        UserRpcVO user = userRemoteService.getUser(transport.getUserId());
                        int count = customFirstOrderRecordMapper.countFinishOrder(user.getCellPhone());
                        if (count <= 0) {
                            isCashPrizeActivityGoods = true;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("校验是否是参与现金奖活动货源异常，货源id:{}", transport.getId(), e);
        }
        return isCashPrizeActivityGoods;
    }

    /**
     * 批量查询货源
     *
     * @param srcMsgIds
     */
    @Override
    public List<TransportMainDO> getBySrcMsgIds(List<Long> srcMsgIds) {
        QueryWrapper<TransportMainDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", srcMsgIds);
        return transportMainMapper.selectList(queryWrapper);
    }

    /**
     * 获取当前用户发布的今日货源
     *
     * @param userId
     */
    @Override
    public List<TransportMainDO> getTodayPublishTransport(Long userId) {
        return transportMainMapper.getTodayPublishTransport(userId);
    }


    /**
     * 查询用户当天发布中货源量
     *
     * @param userId
     * @return
     */
    @Override
    public int getUserDayPublishCount(Long userId) {
        return transportMainMapper.getUserDayPublishCount(userId);
    }

    @Override
    public int getTransportCountForUserId(TransportCountDTO transportCountDTO) {
        return transportMainMapper.getTransportCountForUserId(transportCountDTO);
    }

    /**
     * 隐藏历史货源，不显示
     *
     * @param srcMsgId
     */
    @Override
    public void hideHistoryTransport(Long srcMsgId) {
        transportMainMapper.updateDisplayTypeToHide(srcMsgId);
    }

    @Override
    public void updateStatusById(Long srcMsgId, Integer status, String infoStatus, int display) {
        transportMainMapper.updateStatusById(status, display, infoStatus, srcMsgId);
    }

    public void updateLabelJsonBySrcMsgId(Long srcMsgId, String labelJson) {
        transportMainMapper.updateLabelJsonBySrcMsgId(srcMsgId, labelJson);
        transportMapper.updateLabelJsonBySrcMsgId(srcMsgId, labelJson);
    }

    @Override
    public TransportMainDO getLastTransport(Long userId) {
        return transportMainMapper.getLastTransport(userId);
    }

    /**
     * 删除我的货源
     *
     * @param srcMsgId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMyGoods(Long srcMsgId) {
        transportMainMapper.update(new LambdaUpdateWrapper<TransportMainDO>()
                .set(TransportMainDO::getMtime, new Date())
                .set(TransportMainDO::getIsDelete, 1)
                .eq(TransportMainDO::getId, srcMsgId));
        transportMapper.update(new LambdaUpdateWrapper<TransportDO>()
                .set(TransportDO::getMtime, new Date())
                .set(TransportDO::getIsDelete, 1)
                .eq(TransportDO::getSrcMsgId, srcMsgId));
    }

    private List<Long> getTopNSrcMsgId(String date, Long userId, int topN) {
        return transportMapper.getTopNSrcMsgId(date, userId, topN);
    }

    @Override
    public int updateTransportMain(TransportMainDO transportMain) {
        return transportMainMapper.updateTransportMain(transportMain);
    }

    @Override
    public List<TransportMainDO> getSimilarityForOrderCancelPush(TransportMainDO transportMain, List<Long> excludeSrcMsgIds) {
        return transportMainMapper.getSimilarityForOrderCancelPush(transportMain, excludeSrcMsgIds);
    }

    @Override
    public List<Long> getTransportForUser(Long userId, Integer status, Date startTime, Date endTime) {
        // 如果开始时间为空，默认为当天0点
        startTime = startTime == null ? DateUtil.beginOfDay(new Date()) : startTime;
        return transportMainMapper.getTransportForUser(userId, status, startTime, endTime);
    }
}
