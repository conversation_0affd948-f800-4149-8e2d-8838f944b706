package com.teyuntong.goods.service.service.biz.publish.service.impl;

import com.teyuntong.goods.service.service.biz.publish.mybatis.entity.TransportPricePerkConfigDO;
import com.teyuntong.goods.service.service.biz.publish.mybatis.mapper.TransportPricePerkConfigMapper;
import com.teyuntong.goods.service.service.biz.publish.service.TransportPricePerkConfigService;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 运费补贴配置表 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-05-29
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportPricePerkConfigServiceImpl implements TransportPricePerkConfigService {

    private final TransportPricePerkConfigMapper transportPricePerkConfigMapper;

    @Override
    public TransportPricePerkConfigDO getConfigByCity(String startCity, String destCity) {
        return transportPricePerkConfigMapper.getConfigByCity(startCity, destCity);
    }
}
