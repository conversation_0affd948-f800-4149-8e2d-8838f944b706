package com.teyuntong.goods.service.service.biz.transport.service;


import com.teyuntong.goods.service.client.quotedprice.dto.QuotedPriceDTO;
import com.teyuntong.goods.service.client.quotedprice.vo.*;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportQuotedPriceDO;

import java.util.List;

/**
 * <p>
 * 无价货源报价表 服务类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-11-05
 */
public interface TransportQuotedPriceService {
    /**
     * 车获取某个货源自己的报价
     *
     * @param carUserId
     * @param srcMsgId
     * @return
     */
    TransportQuotedPriceCarVO getCarToTransportQuotedPrice(Long carUserId, Long srcMsgId);

    /**
     * 货获取某个货源的所有报价列表
     *
     * @param srcMsgId
     * @param userId
     * @return
     */
    List<TransportQuotedPriceTransportVO> getTransportQuotedPriceList(Long srcMsgId, Long userId);

    /**
     * 校验用户是否在出价ab测
     *
     * @param userId
     * @return
     */
    Boolean checkUserIsInTransportQuotedPriceABTest(Long userId);

    /**
     * 判断要出价货源是否有效
     *
     * @param srcMsgId
     * @param errorThrow
     * @return
     */
    Boolean checkTransportValidityV2(Long srcMsgId, boolean errorThrow);

    // /**
    //  * 判断要出价货源是否有效
    //  *
    //  * @param srcMsgId
    //  * @return
    //  */
    // CheckTransportValidityVO checkTransportValidity(Long srcMsgId);

    /**
     * 获取货源出价次数
     *
     * @param srcMsgId
     * @return
     */
    Integer getTransportQuotedPriceCountBySrcMsgId(Long srcMsgId);

    /**
     * 货源详情报价列表
     *
     * @param srcMsgId
     * @param userId
     * @return
     */
    List<TransportQuotedPriceTransportVO> getQuotedPriceListInSingleDetailPage(Long srcMsgId, Long userId);

    /**
     * 货主同意报价
     *
     * @param agreeDTO
     */
    QuotedPriceResultVO transportAgree(QuotedPriceDTO agreeDTO);

    /**
     * 货主拒绝/回价
     *
     * @param priceDTO
     */
    void transportQuotedPrice(QuotedPriceDTO priceDTO);

    /**
     * 车主出价
     *
     * @param priceDTO
     */
    QuotedPriceResultVO carQuotedPrice(QuotedPriceDTO priceDTO);

    /**
     * 车主同意报价
     *
     * @param priceDTO
     */
    void carAgree(QuotedPriceDTO priceDTO);

    /**
     * 货报价挽留弹窗
     *
     * @param srcMsgId
     * @return
     */
    TransportQuotedPriceLeaveTabVO getTransportQuotedPriceLeaveTab(Long srcMsgId);

    /**
     * 货方拒绝报价弹窗数据接口
     *
     * @param transportQuotedPriceId
     * @return
     */
    TransportQuotedPriceTabDataVO getTransportQuotedPriceTabData(Long transportQuotedPriceId);

    /**
     * 查询货源遵循车房报价并完成报价数量
     *
     * @param srcMsgId
     * @return
     */
    int getTransportHaveOptionQuotedPriceCount(Long srcMsgId);

    boolean getCarIsHaveQuotedPriceToTransport(Long userId, Long srcMsgId);

    void recordCarLeaveTransportSingleDetail(Long userId, Long srcMsgId);

    /**
     * 货获取所有发布中货源的所有报价列表
     *
     * @param userId
     * @return
     */
    List<AllTransportQuotedPriceVO> getAllPublishingTransportQuotedPriceList(Long userId);

    RecordTypeVO transportNewestRecordType(Long userId);

    Boolean getTransportHaveAnyQuotedPrice(Long userId);

    Boolean getCarHaveNewTransportQuotedPrice(Long userId);

    Boolean getCarLeaveTransportSingleDetailTabData(Long srcMsgId, Long userId);

    String getTransportQuotedPricePageWord(Long srcMsgId);

    /**
     * 查询所有未同意的车主报价
     *
     * @param srcMsgId  货源ID
     * @param userId    货主ID
     * @return
     */
    List<TransportQuotedPriceDO> getCarQuotedPriceList(Long srcMsgId, Long userId);
}
