package com.teyuntong.goods.service.service.biz.transport.service.impl;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.goods.service.client.publish.dto.DirectPublishDTO;
import com.teyuntong.goods.service.client.publish.service.TransportDirectPublishRpcService;
import com.teyuntong.goods.service.client.quotedprice.dto.QuotedPriceDTO;
import com.teyuntong.goods.service.client.quotedprice.vo.*;
import com.teyuntong.goods.service.client.transport.vo.CarryPriceVO;
import com.teyuntong.goods.service.client.transport.vo.TransportCarryReq;
import com.teyuntong.goods.service.service.biz.archive.service.DwsGoodTypeCntService;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.CallPhoneRecordDO;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.AppCallLogService;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.CallPhoneRecordService;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.TransportDispatchViewDetailService;
import com.teyuntong.goods.service.service.biz.exposure.mybatis.entity.ExposureCardGiveawayRecordDO;
import com.teyuntong.goods.service.service.biz.exposure.service.ExposureCardGiveawayService;
import com.teyuntong.goods.service.service.biz.order.dto.SameTransportAvgPriceQueryDTO;
import com.teyuntong.goods.service.service.biz.order.dto.SameTransportAvgPriceResultDTO;
import com.teyuntong.goods.service.service.biz.order.service.TransportAfterOrderDataService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.QuotedPriceBiDataDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportQuotedPriceDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportMainExtendMapper;
import com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportQuotedPriceMapper;
import com.teyuntong.goods.service.service.biz.transport.service.*;
import com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant;
import com.teyuntong.goods.service.service.common.constant.RedisKeyConstant;
import com.teyuntong.goods.service.service.common.enums.*;
import com.teyuntong.goods.service.service.common.utils.TimeUtil;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.inner.GoodsPushRemoteService;
import com.teyuntong.goods.service.service.remote.order.FeedBackRemoteService;
import com.teyuntong.goods.service.service.remote.outer.AutoCallTaskRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserPermissionRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserRemoteService;
import com.teyuntong.infra.basic.resource.client.tytabtest.dto.ABTestDto;
import com.teyuntong.infra.basic.resource.client.tytabtest.vo.ABTestVo;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.infra.common.message.bean.MessagePushBase;
import com.teyuntong.infra.common.message.bean.NewsMessagePush;
import com.teyuntong.infra.common.message.bean.NotifyMessagePush;
import com.teyuntong.infra.common.message.bean.ShortMessageBean;
import com.teyuntong.infra.common.message.enums.NativePageEnum;
import com.teyuntong.infra.common.message.service.MessageCenterService;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import com.teyuntong.inner.export.service.client.push.dto.GoodsPushDto;
import com.teyuntong.inner.export.service.client.push.enums.PushCodeEnum;
import com.teyuntong.inner.export.service.client.push.enums.PushTypeEnum;
import com.teyuntong.outer.export.service.client.common.old.bean.ResultMsgBean;
import com.teyuntong.outer.export.service.client.cticloud.task.vo.AutoCallTaskRequest;
import com.teyuntong.trade.service.client.feedBack.dto.UserFeedbackRatingAndLabelDTO;
import com.teyuntong.user.service.client.permission.dto.UserPermissionRpcDTO;
import com.teyuntong.user.service.client.permission.enums.UserPermissionStatusEnum;
import com.teyuntong.user.service.client.permission.enums.UserPermissionTypeEnum;
import com.teyuntong.user.service.client.permission.vo.UserPermissionRpcVO;
import com.teyuntong.user.service.client.user.vo.ApiDataUserCreditInfoRpcVO;
import com.teyuntong.user.service.client.user.vo.CreditUserRpcVO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.*;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant.TRANSPORT_QUOTED_PRICE_LEAVE_TAB_SHOW_ON_OFF;
import static com.teyuntong.goods.service.service.common.constant.RedisKeyConstant.*;
import static com.teyuntong.goods.service.service.common.error.GoodsErrorCode.*;

/**
 * <p>
 * 无价货源报价表 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-11-05
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportQuotedPriceServiceImpl implements TransportQuotedPriceService {
    @Autowired
    private TransportQuotedPriceMapper transportQuotedPriceMapper;
    @Autowired
    private AppCallLogService appCallLogService;
    @Autowired
    @Lazy
    private TransportMainService transportMainService;
    @Autowired
    private TransportMainExtendMapper transportMainExtendMapper;
    @Autowired
    private DwsGoodTypeCntService dwsGoodTypeCntService;
    @Autowired
    private TransportDispatchViewDetailService transportDispatchViewDetailService;
    @Autowired
    private QuotedModifyPriceLogService quotedModifyPriceLogService;
    @Autowired
    private ThPriceService thPriceService;
    @Autowired
    private QuotedPriceBiDataService quotedPriceBiDataService;
    @Autowired
    private CallPhoneRecordService callPhoneRecordService;
    @Autowired
    private TransportValuableService transportValuableService;
    @Autowired
    private ExposureCardGiveawayService exposureCardGiveawayService;
    @Autowired
    @Lazy
    private TransportDirectPublishRpcService transportDirectPublishRpcService;
    @Autowired
    @Lazy
    private TransportAfterOrderDataService transportAfterOrderDataService;

    @Autowired
    private TytConfigRemoteService tytConfigRemoteService;
    @Autowired
    private ABTestRemoteService abTestRemoteService;
    @Autowired
    private UserRemoteService userRemoteService;
    @Autowired
    private UserPermissionRemoteService userPermissionRemoteService;
    @Autowired
    private FeedBackRemoteService feedBackRemoteService;
    @Autowired
    private GoodsPushRemoteService goodsPushRemoteService;
    @Autowired
    private MessageCenterService messageCenterService;
    @Autowired
    private AutoCallTaskRemoteService autoCallTaskRemoteService;

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 货方回价
     */
    public static final int CARGO_OWNER_REPLY = 1;
    /**
     * 货方同意报价
     */
    public static final int CARGO_OWNER_AGREE = 2;
    public static final String TRANSPORT_QUOTED_PRICE_TOTAL_TIMES_KEY = "transport_quoted_price_total_times";
    public static final String QUOTED_PRICE_NOT_AGREE_CAN_TEL = "您的出价已被驳回，可以电话联系";
    public static final String QUOTED_PRICE_NOT_AGREE = "您的出价已被驳回，可以重新出价";
    public static final String QUOTED_PRICE_IS_AGREE = "您的出价已被接受，快支付订金成交吧";

    /**
     * 车获取某个货源自己的报价
     *
     * @param carUserId
     * @param srcMsgId
     * @return
     */
    @Override
    public TransportQuotedPriceCarVO getCarToTransportQuotedPrice(Long carUserId, Long srcMsgId) {
        TransportQuotedPriceCarVO result = new TransportQuotedPriceCarVO();

        //如果货源不是无价货源或者优车2.0货源或者有效货源，直接返回空
        if (!checkTransportValidityV2(srcMsgId, false)) {
            return null;
        }
        //判断发货人和找货人是否是同一个人
        TransportMainDO transportMain = transportMainService.getTransportMainForId(srcMsgId);
        if (transportMain.getUserId().compareTo(carUserId) == 0) {
            return null;
        }

        //获取配置的最大报价总次数，如果配置不存在或者小于1，则直接返回1
        Integer quotedPriceTotalTimes = getQuotedPriceTotalTimes();
        TransportQuotedPriceDO tytTransportQuotedPrice = transportQuotedPriceMapper.getQuotedPriceByCarUserIdAndTransportMainId(carUserId, srcMsgId);

        //从未出过价
        if (tytTransportQuotedPrice == null) {
            result.setCarIsQuotedPriceOnce(false);
            return result;
        }
        BeanUtils.copyProperties(tytTransportQuotedPrice, result);
        result.setCarIsQuotedPriceOnce(true);
        //报价已被接受
        if (result.getFinalQuotedPriceIsDone() == 1) {
            return result;
        }

        //货方未响应
        if (result.getCarQuotedPriceTimes() > result.getTransportQuotedPriceTimes()) {
            result.setTransportIsQuotedPrice(false);
            return result;
        }
        result.setTransportIsQuotedPrice(true);

        //查看carUserId是否在车无价货源报价AB测试中，不在ab测试中的不可报价
        boolean userIsInTransportQuotedPriceABTest = checkUserIsInTransportQuotedPriceABTest(carUserId);
        if (!userIsInTransportQuotedPriceABTest) {
            result.setCanQuotedPrice(false);
            return result;
        }

        //车方出价次数用完，不能再进行出价
        if (result.getCarQuotedPriceTimes() >= quotedPriceTotalTimes) {
            result.setCanQuotedPrice(false);
            return result;
        }
        result.setCanQuotedPrice(true);

        redisUtil.delete(TRANSPORT_QUOTED_PRICE_CAR_NO_LOOK_HASH_KEY + ":" + carUserId + ":" + srcMsgId);

        return result;
    }

    /**
     * 校验用户是否在出价ab测
     *
     * @param carUserId
     * @return
     */
    @Override
    public Boolean checkUserIsInTransportQuotedPriceABTest(Long carUserId) {
        List<String> codeList = new ArrayList<>();
        codeList.add("quoted_price_car");
        ABTestDto abTestDto = new ABTestDto();
        abTestDto.setCodeList(codeList);
        abTestDto.setUserId(carUserId);
        List<ABTestVo> userTypeList = abTestRemoteService.getUserTypeList(abTestDto);
        if (CollectionUtils.isNotEmpty(userTypeList)) {
            return userTypeList.get(0).getType().equals(1);
        }
        return false;
    }

    public Integer getQuotedPriceTotalTimes() {
        Integer quotedPriceTotalTimes = tytConfigRemoteService.getIntValue(ConfigKeyConstant.TRANSPORT_QUOTED_PRICE_TOTAL_TIMES_KEY, 1);
        if (quotedPriceTotalTimes == null || quotedPriceTotalTimes < 1) {
            quotedPriceTotalTimes = 1;
        }
        return quotedPriceTotalTimes;
    }


    /**
     * 判断要出价货源是否有效
     *
     * @param srcMsgId      货源ID
     * @param errorThrow    校验失败是否抛出业务异常
     * @return
     */
    @Override
    public Boolean checkTransportValidityV2(Long srcMsgId, boolean errorThrow) {
        TransportMainDO transportMain = transportMainService.getTransportMainForId(srcMsgId);
        if (transportMain != null && transportMain.getStatus() != null && transportMain.getPubDate() != null) {
            if (!Objects.equals(TransportStatusEnum.VALID.getCode(), transportMain.getStatus()) ||
                    !TimeUtil.isToday(transportMain.getPubDate().getTime())) {
                // 状态为无效
                if (errorThrow) {
                    throw new BusinessException(ERROR_TRANSPORT_INVALID, null);
                } else {
                    return false;
                }
            }

            boolean isHavePriceRuleTransport = false;
            // 车方查看一口价货源是判断是否可以看到报价相关组件，非一口价货源由app来判断
            Integer priceType = transportMainExtendMapper.getPriceTypeBySrcMsgId(srcMsgId);
            if (!Objects.equals(transportMain.getInvoiceTransport(), 1)
                    && (!Objects.equals(transportMain.getExcellentGoods(), ExcellentGoodsEnums.SPECIAL.getCode()) ||
                        (Objects.equals(priceType, PriceTypeEnum.FLEXIBLE.getCode())))) {
                isHavePriceRuleTransport = true;
            }

            if (!isHavePriceRuleTransport) {
                if (errorThrow) {
                    throw new BusinessException(TRANSPORT_QUOTED_NOT_PERMIT, null);
                } else {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 查询货源出价次数
     *
     * @param srcMsgId
     * @return
     */
    @Override
    public Integer getTransportQuotedPriceCountBySrcMsgId(Long srcMsgId) {
        return transportQuotedPriceMapper.getTransportQuotedPriceCountBySrcMsgId(srcMsgId);
    }

    @Override
    public boolean getCarIsHaveQuotedPriceToTransport(Long userId, Long srcMsgId) {
        Integer count = transportQuotedPriceMapper.getCarIsHaveQuotedPriceToTransport(userId, srcMsgId);
        return count != null && count > 0;
    }

    @Override
    public void recordCarLeaveTransportSingleDetail(Long carUserId, Long srcMsgId) {
        long secondsUntilTomorrow = TimeUtil.getTomorrowZeroSeconds();
        String redisKey = TRANSPORT_QUOTED_PRICE_CAR_LEAVE_HASH_KEY + ":" + carUserId;
        Boolean hasKey = stringRedisTemplate.hasKey(redisKey);
        if (Boolean.FALSE.equals(hasKey)) {
            stringRedisTemplate.opsForHash().put(redisKey, srcMsgId.toString(), "1");
            stringRedisTemplate.expire(redisKey, Duration.ofSeconds(secondsUntilTomorrow));
        } else {
            stringRedisTemplate.opsForHash().put(redisKey, srcMsgId.toString(), "1");
        }
    }

    @Override
    public int getTransportHaveOptionQuotedPriceCount(Long srcMsgId) {
        return transportQuotedPriceMapper.getTransportHaveOptionQuotedPriceCount(srcMsgId);
    }

    /**
     * 货源详情报价列表
     *
     * @param srcMsgId
     * @param userId
     * @return
     */
    @Override
    public List<TransportQuotedPriceTransportVO> getQuotedPriceListInSingleDetailPage(Long srcMsgId, Long userId) {
        List<TransportQuotedPriceTransportVO> quotedPricetTransportVOList = getTransportQuotedPriceTransportVOList(srcMsgId);
        if (CollectionUtils.isEmpty(quotedPricetTransportVOList)) {
            return quotedPricetTransportVOList;
        }
        List<TransportQuotedPriceTransportVO> result = quotedPricetTransportVOList.stream().limit(3).collect(Collectors.toList());
        Object noLookQuotedPriceNumObj = redisUtil.hashGet(RedisKeyConstant.TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + userId, srcMsgId.toString());
        if (Objects.nonNull(noLookQuotedPriceNumObj)) {
            String noLookQuotedPriceNumString = noLookQuotedPriceNumObj.toString();
            if (StringUtils.isNumeric(noLookQuotedPriceNumString)) {
                int noLookQuotedPriceNum = Integer.parseInt(noLookQuotedPriceNumString);
                for (int i = 0; i < Math.min(noLookQuotedPriceNum, result.size()); i++) {
                    TransportQuotedPriceTransportVO transportQuotedPriceTransportVO = result.get(i);
                    transportQuotedPriceTransportVO.setTransportNoLook(true);
                }
            }
        }
        makeCarUserData(result);
        return result;
    }

    /**
     * 获取货源最新一条是出价记录还是沟通记录
     *
     * @param userId
     * @return
     */
    @Override
    public RecordTypeVO transportNewestRecordType(Long userId) {
        RecordTypeVO result = new RecordTypeVO();
        int recordType = 0;

        try {
            List<Long> srcMsgIds =  transportMainService.getInReleaseTransportIdList(userId);
            if (CollectionUtils.isNotEmpty(srcMsgIds)) {
                Optional<CallPhoneRecordDO> callPhoneRecordOpt = Optional.ofNullable(callPhoneRecordService.getLatestCallRecord(srcMsgIds));
                Optional<TransportQuotedPriceDO> quotedPriceOpt = Optional.ofNullable(transportQuotedPriceMapper.getLatestQuotedRecord(srcMsgIds));

                if (callPhoneRecordOpt.isPresent() && quotedPriceOpt.isPresent()) {
                    Instant callTime = callPhoneRecordOpt.get().getCreateTime().toInstant();
                    Instant quoteTime = quotedPriceOpt.get().getCarQuotedPriceTime().toInstant();

                    if (callTime.isAfter(quoteTime)) {
                        recordType = 1;
                    } else {
                        recordType = 2;
                    }
                } else if (callPhoneRecordOpt.isPresent()) {
                    recordType = 1;
                } else if (quotedPriceOpt.isPresent()) {
                    recordType = 2;
                }
            }
        } catch (Exception e) {
            log.error("transportNewestRecordType error: {}", e.getMessage(), e);
        }

        result.setRecordType(recordType);
        return result;
    }

    /**
     * 货主是否有货源被车方出价
     *
     * @param userId
     * @return
     */
    @Override
    public Boolean getTransportHaveAnyQuotedPrice(Long userId) {
        List<Long> haveQuotedPriceSrcMsgIdList = transportQuotedPriceMapper.getTransportHaveAnyQuotedPrice(userId);
        if (CollectionUtils.isEmpty(haveQuotedPriceSrcMsgIdList)) {
            return false;
        }
        List<Long> inReleaseSrcMsgIdList = transportMainService.getInReleaseTransport(haveQuotedPriceSrcMsgIdList);
        if (CollectionUtils.isNotEmpty(inReleaseSrcMsgIdList)) {
            return true;
        }
        return false;
    }

    /**
     * 车主是否存在货方回价了但车方还没想响应的货
     *
     * @param userId
     * @return
     */
    @Override
    public Boolean getCarHaveNewTransportQuotedPrice(Long userId) {
        List<Long> haveQuotedPriceSrcMsgIdList = transportQuotedPriceMapper.getCarHaveNewTransportQuotedPrice(userId);
        if (CollectionUtils.isEmpty(haveQuotedPriceSrcMsgIdList)) {
            return false;
        }
        List<Long> inReleaseSrcMsgIdList = transportMainService.getInReleaseTransport(haveQuotedPriceSrcMsgIdList);
        if (CollectionUtils.isNotEmpty(inReleaseSrcMsgIdList)) {
            return true;
        }
        return false;
    }

    /**
     * 车离开货源详情报价挽留弹窗
     *
     * @param srcMsgId
     * @return
     */
    @Override
    public Boolean getCarLeaveTransportSingleDetailTabData(Long srcMsgId, Long carUserId) {
        Boolean hasKey = stringRedisTemplate.opsForHash().hasKey(TRANSPORT_QUOTED_PRICE_CAR_LEAVE_HASH_KEY + ":" + carUserId, srcMsgId.toString());
        if (hasKey) {
            return false;
        }

        TransportQuotedPriceDO quotedPrice = transportQuotedPriceMapper.getQuotedPriceByCarUserIdAndTransportMainId(carUserId, srcMsgId);
        if (quotedPrice != null) {
            return false;
        }
        recordCarLeaveTransportSingleDetail(carUserId, srcMsgId);
        return true;
    }

    /**
     * 货在报价列表web页面顶部氛围文案
     *
     * @param srcMsgId
     * @return
     */
    @Override
    public String getTransportQuotedPricePageWord(Long srcMsgId) {
        TransportMainDO bySrcMsgId = transportMainService.getTransportMainForId(srcMsgId);
        if (bySrcMsgId == null) {
            return "";
        }
        int similarityGoods = transportMainService.countSimilarityGoods(srcMsgId);
        if (similarityGoods > 0) {
            return "此货源多人发布，加价可提前锁定司机";
        } else {
            int sameRouteGoods = transportMainService.countSameRouteGoods(srcMsgId);
            if (sameRouteGoods > 0) {
                return "当前同路线货源多，合理加价找车更快";
            }
        }
        return "";
    }

    /**
     * 查询所有未同意的车主报价
     *
     * @param srcMsgId              货源ID
     * @param transportUserId       货主ID
     * @return
     */
    @Override
    public List<TransportQuotedPriceDO> getCarQuotedPriceList(Long srcMsgId, Long transportUserId) {
        return transportQuotedPriceMapper.getCarQuotedPriceList(srcMsgId, transportUserId);
    }

    /**
     * 货获取所有发布中货源的所有报价列表
     *
     * @param userId
     * @return
     */
    @Override
    public List<AllTransportQuotedPriceVO> getAllPublishingTransportQuotedPriceList(Long userId) {
        List<AllTransportQuotedPriceVO> result = new ArrayList<>();

        List<Long> transportIdList = transportMainService.getInReleaseTransportIdList(userId);
        if (CollectionUtils.isEmpty(transportIdList)) {
            return result;
        }

        List<TransportQuotedPriceDO> transportQuotedPriceDOS = transportQuotedPriceMapper.getAllQuotedPriceListBySrcMsgIdList(transportIdList);
        if (CollectionUtils.isEmpty(transportQuotedPriceDOS)) {
            return  result;
        }

        String tytServerPictureUrl = tytConfigRemoteService.getStringValue("tyt_server_picture_url_old", "http://www.teyuntong.com/rootdata");
        Integer quotedPriceTotalTimes = getQuotedPriceTotalTimes();

        ExecutorService executorService = Executors.newFixedThreadPool(5, new ThreadFactory() {
            private final AtomicInteger threadNumber = new AtomicInteger(1);
            @Override
            public Thread newThread(Runnable r) {
                return new Thread(r, "quotedPrice-thread-" + threadNumber.getAndIncrement());
            }
        });

        CountDownLatch latch = new CountDownLatch(transportQuotedPriceDOS.size());
        List<Future<AllTransportQuotedPriceVO>> futures = new ArrayList<>();

        for (TransportQuotedPriceDO tytTransportQuotedPrice : transportQuotedPriceDOS) {
            Future<AllTransportQuotedPriceVO> future = executorService.
                    submit(new TransportQuotedPriceVoTask(tytTransportQuotedPrice, tytServerPictureUrl, quotedPriceTotalTimes, latch));
            futures.add(future);
        }
        try {
            if (!latch.await(5, TimeUnit.SECONDS)) {
                log.warn("获取所有发布中货源出价记录, CountDownLatch等待超时, userId:{}", userId);
            }
        } catch (InterruptedException e) {
            log.error("获取所有发布中货源出价记录 CountDownLatch await InterruptedException, userId:{}", userId, e);
            Thread.currentThread().interrupt();
            throw new RuntimeException(e);
        }

        for (Future<AllTransportQuotedPriceVO> future : futures) {
            try {
                AllTransportQuotedPriceVO allTransportQuotedPriceVo = future.get(3, TimeUnit.SECONDS);
                if (allTransportQuotedPriceVo != null) {
                    if (allTransportQuotedPriceVo.getQuotedType() != null && allTransportQuotedPriceVo.getQuotedType() == 0
                            && allTransportQuotedPriceVo.getFinalQuotedPriceIsDone() != null && allTransportQuotedPriceVo.getFinalQuotedPriceIsDone() == 1) {
                        allTransportQuotedPriceVo.setHaveCallToCarButton(1);
                    }
                    result.add(allTransportQuotedPriceVo);
                }
            } catch (Exception e) {
                log.error("获取所有发布中货源出价记录，从futures中获取数据异常,userId:{}", userId, e);
            }
        }
        executorService.shutdown();

        try {
            // 货主查看本人货源所有报价
            Boolean hasKey = stringRedisTemplate.hasKey(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + userId);
            if (Objects.nonNull(hasKey) && hasKey) {
                // 将所有货源的货主未浏览报价次数清空
                stringRedisTemplate.delete(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + userId);
            }
        } catch (Exception e) {
            log.error("获取所有发布中货源出价记录，redis处理异常,{}", userId, e);
        }

        return result;
    }

    class TransportQuotedPriceVoTask implements Callable<AllTransportQuotedPriceVO> {
        private final TransportQuotedPriceDO tytTransportQuotedPrice;
        private final String tytServerPictureUrl;
        private final int quotedPriceTotalTimes;
        private final CountDownLatch countDownLatch;

        public TransportQuotedPriceVoTask(TransportQuotedPriceDO tytTransportQuotedPrice,
                                          String tytServerPictureUrl,
                                          int quotedPriceTotalTimes,
                                          CountDownLatch countDownLatch) {
            this.tytTransportQuotedPrice = tytTransportQuotedPrice;
            this.tytServerPictureUrl = tytServerPictureUrl;
            this.quotedPriceTotalTimes = quotedPriceTotalTimes;
            this.countDownLatch = countDownLatch;
        }

        @Override
        public AllTransportQuotedPriceVO call() {
            AllTransportQuotedPriceVO allTransportQuotedPriceVo = new AllTransportQuotedPriceVO();
            try {
                BeanUtils.copyProperties(tytTransportQuotedPrice, allTransportQuotedPriceVo);

                allTransportQuotedPriceVo.setId(tytTransportQuotedPrice.getId());
                allTransportQuotedPriceVo.setCarUserName(tytTransportQuotedPrice.getCarUserName());
                allTransportQuotedPriceVo.setCarQuotedPrice(tytTransportQuotedPrice.getCarQuotedPrice());
                allTransportQuotedPriceVo.setCarQuotedPriceTime(tytTransportQuotedPrice.getCarQuotedPriceTime());
                allTransportQuotedPriceVo.setGoodCarPriceTransport(0);
                //返回货源信息
                TransportMainDO transportMain = transportMainService.getTransportMainForId(tytTransportQuotedPrice.getSrcMsgId());
                if (transportMain != null) {
                    allTransportQuotedPriceVo.setStartPoint(transportMain.getStartPoint());
                    allTransportQuotedPriceVo.setDestPoint(transportMain.getDestPoint());
                    allTransportQuotedPriceVo.setTaskContent(transportMain.getTaskContent());
                    allTransportQuotedPriceVo.setWeight(transportMain.getWeight());
                    allTransportQuotedPriceVo.setLength(transportMain.getLength());
                    allTransportQuotedPriceVo.setWide(transportMain.getWide());
                    allTransportQuotedPriceVo.setHigh(transportMain.getHigh());
                    allTransportQuotedPriceVo.setPrice(transportMain.getPrice());
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(transportMain.getLabelJson())
                            && JSONObject.parseObject(transportMain.getLabelJson()).containsKey("goodCarPriceTransport")
                            && JSONObject.parseObject(transportMain.getLabelJson()).getInteger("goodCarPriceTransport") == 1) {
                        allTransportQuotedPriceVo.setGoodCarPriceTransport(1);
                    }
                }

                fillSingleVO(tytServerPictureUrl, allTransportQuotedPriceVo);
                if (Objects.nonNull(transportMain)) {
                    allTransportQuotedPriceVo.setGoodsTypeName(transportMain.getGoodTypeName());
                    Integer goodsTypeNum = dwsGoodTypeCntService.getGoodsTypeCnt(tytTransportQuotedPrice.getCarId(), transportMain.getGoodTypeName());
                    allTransportQuotedPriceVo.setGoodsTypeNum(goodsTypeNum);
                }

                // 查询是否发放曝光卡
                List<ExposureCardGiveawayRecordDO> giveawayRecordDOS = exposureCardGiveawayService.queryGiveawayRecord(tytTransportQuotedPrice.getSrcMsgId(), 1);
                allTransportQuotedPriceVo.setGiveawayExposureCard(CollectionUtils.isEmpty(giveawayRecordDOS) ? 0 : 1);

                //报价已被接受
                if (allTransportQuotedPriceVo.getFinalQuotedPriceIsDone() == 1) {
                    return allTransportQuotedPriceVo;
                }

                //车方未响应
                if (allTransportQuotedPriceVo.getCarQuotedPriceTimes().equals(allTransportQuotedPriceVo.getTransportQuotedPriceTimes())) {
                    allTransportQuotedPriceVo.setCarIsQuotedPriceAgain(false);
                    return allTransportQuotedPriceVo;
                }
                allTransportQuotedPriceVo.setCarIsQuotedPriceAgain(true);

                //货方出价次数用完，不能再进行出价
                if (allTransportQuotedPriceVo.getTransportQuotedPriceTimes() >= quotedPriceTotalTimes) {
                    allTransportQuotedPriceVo.setCanQuotedPrice(false);
                    return allTransportQuotedPriceVo;
                }
                allTransportQuotedPriceVo.setCanQuotedPrice(true);

            } catch (Exception e) {
                log.error("获取所有发布中货源出价记录, 多线程执行出价记录输出组装失败，出价ID:{}", tytTransportQuotedPrice.getId(), e);
            } finally {
                countDownLatch.countDown();
            }
            return allTransportQuotedPriceVo;
        }
    }

    /**
     * 货报价挽留弹窗
     *
     * @param srcMsgId
     * @return
     */
    @Override
    public TransportQuotedPriceLeaveTabVO getTransportQuotedPriceLeaveTab(Long srcMsgId) {
        TransportQuotedPriceLeaveTabVO leaveTabVO = new TransportQuotedPriceLeaveTabVO();
        leaveTabVO.setShowLeaveTab(false);

        Integer leaveTabShowOnOff = tytConfigRemoteService.getIntValue(TRANSPORT_QUOTED_PRICE_LEAVE_TAB_SHOW_ON_OFF, 0);
        if (!Objects.equals(leaveTabShowOnOff, 1)) {
            return leaveTabVO;
        }

        String leaveKey = redisUtil.getString(TRANSPORT_QUOTED_PRICE_TRANSPORT_LEAVE_KEY + ":" + srcMsgId);
        if (Objects.nonNull(leaveKey)) {
            return leaveTabVO;
        }

        int optionQuotedPriceCount = getTransportHaveOptionQuotedPriceCount(srcMsgId);
        Integer transportQuotedPriceCount = getTransportQuotedPriceCountBySrcMsgId(srcMsgId);
        if (optionQuotedPriceCount == 0 && transportQuotedPriceCount != 0) {
            leaveTabVO.setShowLeaveTab(true);

            //相似货源、同路线文案
            String word = "";
            int similarityGoodsCount = transportMainService.countSimilarityGoods(srcMsgId);
            if (similarityGoodsCount > 0) {
                word = "当前相似货源多，";
            } else {
                int sameRouteGoodsCount = transportMainService.countSameRouteGoods(srcMsgId);
                if (sameRouteGoodsCount > 0) {
                    word = "当前同路线货源多，";
                }
            }

            if (transportQuotedPriceCount == 1) {
                TransportQuotedPriceDO quotedPriceById = transportQuotedPriceMapper.getQuotedPriceLastOneBySrcMsgId(srcMsgId);
                leaveTabVO.setTabType(1);
                leaveTabVO.setNum(quotedPriceById.getCarQuotedPrice());
                leaveTabVO.setReason(quotedPriceById.getReason());
                String wordPrefix = "有司机出价" + quotedPriceById.getCarQuotedPrice() + "元";
                if (Objects.equals(QuotedTypeEnum.SYSTEM.getCode(), quotedPriceById.getQuotedType())) {
                    wordPrefix = "刚刚有" + quotedPriceById.getTransportNum() + "票相似货源以" + quotedPriceById.getCarQuotedPrice() + "元成交";
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(leaveTabVO.getReason())) {
                    wordPrefix += "（留言：" + leaveTabVO.getReason() + "）";
                }
                word += wordPrefix + "，可通知司机接单并更新运费";
                leaveTabVO.setQuotedPriceId(quotedPriceById.getId());
            } else {
                leaveTabVO.setTabType(2);
                leaveTabVO.setNum(transportQuotedPriceCount);
                String middle = "名司机";
                int systemQuoted = transportQuotedPriceMapper.countSystemQuotedPrice(srcMsgId);
                if (systemQuoted > 0) {
                    middle = "条";
                }
                word += ("有" + transportQuotedPriceCount + middle + "出价，同意后将通知司机接单");
            }
            TransportMainDO transportMain = transportMainService.getTransportMainForId(srcMsgId);
            if (StringUtils.isBlank(transportMain.getPrice()) || Objects.equals("0", transportMain.getPrice())) {
                word += "，额外获得40分曝光";
            }
            leaveTabVO.setWord(word);
            redisUtil.set(TRANSPORT_QUOTED_PRICE_TRANSPORT_LEAVE_KEY + ":" + srcMsgId, "1", Duration.ofSeconds(60 * 60 * 30));
        }

        return leaveTabVO;
    }

    /**
     * 货方拒绝报价弹窗数据接口
     *
     * @param transportQuotedPriceId
     * @return
     */
    @Override
    public TransportQuotedPriceTabDataVO getTransportQuotedPriceTabData(Long transportQuotedPriceId) {
        TransportQuotedPriceDO quotedPrice = transportQuotedPriceMapper.selectById(transportQuotedPriceId);
        if (Objects.isNull(quotedPrice)) {
            throw new BusinessException(NO_QUOTED_PRICE, null);
        }

        TransportQuotedPriceTabDataVO result = new TransportQuotedPriceTabDataVO();
        result.setSrcMsgId(quotedPrice.getSrcMsgId());

        result.setTitle("司机报价太高？给司机回价");
        result.setSystemQuotedPrice(false);
        if (quotedPrice.getCarId() == -1) {
            result.setSystemQuotedPrice(true);
            int viewLogCount = appCallLogService.getViewLogCountBySrcMsgId(quotedPrice.getSrcMsgId());
            if (viewLogCount > 0) {
                result.setTitle("有" + viewLogCount + "名司机感兴趣，等待您的回价");
            } else {
                result.setTitle("联系司机太少？回价可增加曝光");
            }
        }

        TransportMainDO transportMain = transportMainService.getTransportMainForId(quotedPrice.getSrcMsgId());
        result.setTransportPrice(BigDecimal.ZERO);

        BigDecimal minPrice = new BigDecimal(quotedPrice.getCarQuotedPrice() / 2).setScale(0, RoundingMode.HALF_UP);
        BigDecimal maxPrice = new BigDecimal(quotedPrice.getCarQuotedPrice());
        if (StringUtils.isNotBlank(transportMain.getPrice()) && new BigDecimal(transportMain.getPrice()).compareTo(BigDecimal.ZERO) > 0) {
            //货源有价并且不是系统报价
            minPrice = new BigDecimal(transportMain.getPrice());
            result.setTransportPrice(new BigDecimal(transportMain.getPrice()));
        }

        result.setHaveChangePriceButton(false);
        result.setHavePriceSeekBar(false);
        BigDecimal defaultPrice = maxPrice.multiply(new BigDecimal("0.9")).setScale(0, RoundingMode.HALF_UP);
        if (maxPrice.compareTo(result.getTransportPrice()) > 0) {
            result.setHaveChangePriceButton(true);
            result.setHavePriceSeekBar(true);
            defaultPrice = maxPrice.add(minPrice).divide(new BigDecimal(2), 0, RoundingMode.UP);
        }
        if (quotedPrice.getCarId() == -1 && result.getTransportPrice().compareTo(BigDecimal.ZERO) > 0) {
            //有价货源系统报价无seekbar和修改运费按钮
            result.setHaveChangePriceButton(false);
            result.setHavePriceSeekBar(false);
        }


        if (quotedPrice.getCarId() == -1) {
            //系统报价时默认值取系统报价值
            defaultPrice = maxPrice;
            result.setSubheading("刚刚有" + quotedPrice.getTransportNum() + "票以" + quotedPrice.getCarQuotedPrice() + "元成交");
        } else {
            SameTransportAvgPriceQueryDTO avgPriceQueryDTO = new SameTransportAvgPriceQueryDTO();
            avgPriceQueryDTO.setSrcMsgId(quotedPrice.getSrcMsgId());
            SameTransportAvgPriceResultDTO sameTransportAveragePriceData = transportAfterOrderDataService.getSameTransportAvgPrice(avgPriceQueryDTO);
            if (sameTransportAveragePriceData != null && sameTransportAveragePriceData.getAveragePrice() != null
                    && sameTransportAveragePriceData.getAveragePrice().compareTo(minPrice) > 0
                    && sameTransportAveragePriceData.getAveragePrice().compareTo(maxPrice) < 0) {
                defaultPrice = sameTransportAveragePriceData.getAveragePrice();
                result.setSubheading("平台均价参考：" + defaultPrice + "元");
            }
        }

        result.setMinPrice(minPrice);
        result.setMaxPrice(maxPrice);
        result.setDefaultPrice(defaultPrice);

        return result;
    }

    /**
     * 货主拒绝报价/回价
     *
     * @param priceDTO
     */
    @Override
    public void transportQuotedPrice(QuotedPriceDTO priceDTO) {
        checkTransportValidityV2(priceDTO.getSrcMsgId(), true);

        TransportMainDO main = transportMainService.getTransportMainForId(priceDTO.getSrcMsgId());
        checkCarryPriceIsRules(main, priceDTO.getUserId(), priceDTO.getTransportQuotedPriceId(), priceDTO.getPrice());

        Integer quotedPriceTotalTimes = getQuotedPriceTotalTimes();

        TransportQuotedPriceDO quotedPriceDO = transportQuotedPriceMapper.selectById(priceDTO.getTransportQuotedPriceId());
        if (Objects.isNull(quotedPriceDO)) {
            throw new BusinessException(NO_QUOTED_PRICE, null);
        }
        if (Objects.equals(quotedPriceDO.getFinalQuotedPriceIsDone(), 1)) {
            throw new BusinessException(QUOTED_PRICE_ACCEPTED, null);
        }
        if (quotedPriceDO.getTransportQuotedPriceTimes() >= quotedPriceTotalTimes) {
            throw new BusinessException(QUOTED_PRICE_OVER_TIMES, null);
        }
        if (quotedPriceDO.getCarQuotedPriceTimes() <= quotedPriceDO.getTransportQuotedPriceTimes()) {
            throw new BusinessException(QUOTED_PRICE_REPEATED, null);
        }

        boolean addPrice = false;
        boolean modifyPriceSuccess = false;

        //车方已完成报价，货方驳回，正要进行回价
        transportQuotedPriceMapper.transportQuotedPrice(priceDTO.getTransportQuotedPriceId(), priceDTO.getPrice());

        if (Objects.equals(priceDTO.getChangePrice(), YesOrNoEnum.YES.getId())) {
            if (StringUtils.isNotBlank(main.getPrice()) && new BigDecimal(priceDTO.getPrice()).compareTo(new BigDecimal(main.getPrice())) > 0) {
                addPrice = true;
            } else if (StringUtils.isBlank(main.getPrice()) || new BigDecimal(main.getPrice()).compareTo(BigDecimal.ZERO) == 0) {
                addPrice = true;
            }
            modifyPriceSuccess = updatePriceAndTecServiceFee(priceDTO, priceDTO.getPrice(), main, 1);
        }
        // 发送push
        transportQuotedPriceChangeNeedPush(quotedPriceDO, 1, addPrice && modifyPriceSuccess, main);

        stringRedisTemplate.opsForValue().set(TRANSPORT_QUOTED_PRICE_CAR_NO_LOOK_HASH_KEY + ":" + quotedPriceDO.getCarId() + ":" + quotedPriceDO.getSrcMsgId(), "1", 60 * 60 * 30, TimeUnit.SECONDS);
    }

    /**
     * 车主同意报价
     *
     * @param priceDTO
     */
    @Override
    public void carAgree(QuotedPriceDTO priceDTO) {
        checkTransportValidityV2(priceDTO.getSrcMsgId(), true);

        TransportQuotedPriceDO quotedPriceDO = transportQuotedPriceMapper.getQuotedPriceByCarUserIdAndTransportMainId(priceDTO.getUserId(), priceDTO.getSrcMsgId());
        if (Objects.isNull(quotedPriceDO)) {
            throw new BusinessException(NO_QUOTED_PRICE, null);
        }
        if (Objects.equals(quotedPriceDO.getFinalQuotedPriceIsDone(), 1)) {
            // 该车主对该货源的报价已被接受
            throw new BusinessException(QUOTED_PRICE_ACCEPTED, null);
        }
        if (Objects.isNull(quotedPriceDO.getTransportQuotedPrice())) {
            // 数据错误，货方报价为空，车方不能同意
            throw new BusinessException(ERROR_NO_PARAM, null);
        }
        transportQuotedPriceMapper.carAgree(priceDTO.getUserId(), priceDTO.getSrcMsgId());

        // 记录货方未查看的报价相关逻辑
        TransportMainDO transportMain = transportMainService.getTransportMainForId(priceDTO.getSrcMsgId());
        recordCarQuotedPrice(transportMain);
    }

    /**
     * 车主出价
     *
     * @param priceDTO
     */
    @Override
    public QuotedPriceResultVO carQuotedPrice(QuotedPriceDTO priceDTO) {
        QuotedPriceResultVO result = new QuotedPriceResultVO();
        // 判断发货人和车主是不是同一个
        TransportMainDO main = transportMainService.getTransportMainForId(priceDTO.getSrcMsgId());
        if (Objects.equals(priceDTO.getUserId(), main.getUserId())) {
            throw new BusinessException(QUOTED_PRICE_TRANSPORT_OWNER_NOT_ALLOWED, null);
        }
        if (StringUtils.isNotBlank(main.getPrice()) && priceDTO.getPrice() <= Integer.parseInt(main.getPrice())) {
            throw new BusinessException(QUOTED_PRICE_LOWER_THAN_TRANSPORT_PRICE, null);
        }
        String oldPrice = main.getPrice();

        // 用户是否在车无价货源报价ab测
        Boolean inAbTest = checkUserIsInTransportQuotedPriceABTest(priceDTO.getUserId());
        if (!inAbTest) {
            throw new BusinessException(QUOTED_PRICE_USER_NOT_ALLOWED, null);
        }
        // 判断是否可以出价
        checkTransportValidityV2(priceDTO.getSrcMsgId(), true);
        // 校验出价金额
        checkCarryPriceIsRules(main, priceDTO.getUserId(), null, priceDTO.getPrice());

        UserRpcVO carUser = userRemoteService.getUser(priceDTO.getUserId());
        if (Objects.isNull(carUser)) {
            throw new BusinessException(ERROR_NO_PARAM, null);
        }

        TransportQuotedPriceDO quotedPriceDO = transportQuotedPriceMapper.getQuotedPriceByCarUserIdAndTransportMainId(priceDTO.getUserId(), priceDTO.getSrcMsgId());
        boolean transportNowHaveNoPrice = TransportUtil.nonPrice(main.getPrice());

        if (Objects.isNull(quotedPriceDO)) {
            // 首次报价
            Long quotedPriceId = InsertTransportQuotedPrice(priceDTO, main, carUser);
            // 价值货源出价更新出价状态及出价金额
            transportValuableService.updateTransportValuableDriverGivePrice(priceDTO.getSrcMsgId(), priceDTO.getPrice());
            // 记录货方未查看的报价相关逻辑
            recordCarQuotedPrice(main);
            boolean autoAgree = priceAssistantAutoAgree(priceDTO.getSrcMsgId(), priceDTO.getPrice(), main, quotedPriceId);
            // 给货方发车方出价短信push站内信
            carQuotedPriceSendMessageToTransportUser(main, priceDTO.getPrice(), transportNowHaveNoPrice, autoAgree);
            result.setAutoAgree(autoAgree ? 1 : 0);
            result.setOldPrice(TransportUtil.hasPrice(main.getPrice()) ? Integer.parseInt(oldPrice) : 0);
            result.setNewPrice(priceDTO.getPrice());
            return result;
        }

        if (quotedPriceDO.getFinalQuotedPriceIsDone() == 1) {
            // 该车主对该货源的报价已被接受
            throw new BusinessException(QUOTED_PRICE_ACCEPTED, null);
        }
        Integer quotedPriceTotalTimes = getQuotedPriceTotalTimes();
        if (quotedPriceDO.getCarQuotedPriceTimes() >= quotedPriceTotalTimes) {
            // 车方报价次数已超过允许的报价总次数
            throw new BusinessException(QUOTED_PRICE_OVER_TIMES, null);
        }
        if (quotedPriceDO.getCarQuotedPriceTimes().equals(quotedPriceDO.getTransportQuotedPriceTimes())) {
            // 货方拒绝了车方的报价，车方正要进行回价
            transportQuotedPriceMapper.subsequentCarQuotedPrice(priceDTO.getUserId(),
                                                                carUser.getCarUserName() == null ? carUser.getUserName() : carUser.getCarUserName(),
                                                                priceDTO.getSrcMsgId(), priceDTO.getPrice(), priceDTO.getReason());
            // 价值货源出价更新出价状态及出价金额
            transportValuableService.updateTransportValuableDriverGivePrice(priceDTO.getSrcMsgId(), priceDTO.getPrice());
            // 记录货方未查看的报价相关逻辑
            recordCarQuotedPrice(main);
            boolean autoAgree = priceAssistantAutoAgree(priceDTO.getSrcMsgId(), priceDTO.getPrice(), main, quotedPriceDO.getId());
            // 给货方发车方出价短信push站内信
            carQuotedPriceSendMessageToTransportUser(main, priceDTO.getPrice(), transportNowHaveNoPrice, autoAgree);
            result.setAutoAgree(autoAgree ? 1 : 0);
            result.setOldPrice(TransportUtil.hasPrice(main.getPrice()) ? Integer.parseInt(oldPrice) : 0);
            result.setNewPrice(priceDTO.getPrice());
            return result;
        } else {
            throw new BusinessException(QUOTED_PRICE_CAR_REPEATED, null);
        }
    }

    /**
     * 货主设置了回价助手且满足条件，自动同意车主报价
     *
     * @param srcMsgId
     * @param price
     * @param transportMain
     * @param quotedPriceId
     * @return
     */
    private boolean priceAssistantAutoAgree(Long srcMsgId, Integer price, TransportMainDO transportMain, Long quotedPriceId) {
        TransportMainExtendDO mainExtend = transportMainExtendMapper.getBySrcMsgId(srcMsgId);
        if (Objects.nonNull(mainExtend) && Objects.nonNull(mainExtend.getPriceCap()) && mainExtend.getPriceCap() > 0) {
            if (price <= mainExtend.getPriceCap()) {
                Integer userType = abTestRemoteService.getUserType("publish_price_assistant", transportMain.getUserId());
                if (Objects.equals(userType, 1)) {
                    QuotedPriceDTO priceDTO = new QuotedPriceDTO();
                    priceDTO.setSrcMsgId(srcMsgId);
                    priceDTO.setTransportQuotedPriceId(quotedPriceId);
                    priceDTO.setUserId(transportMain.getUserId());
                    priceDTO.setAutoAgree(1);
                    QuotedPriceResultVO quotedPriceResultVO = this.transportAgree(priceDTO);
                    return quotedPriceResultVO.getSuccess();
                }
            }
        }
        return false;
    }

    /**
     * 车主出价给货主发送消息
     *
     * @param transportMain
     * @param price
     * @param transportNowHaveNoPrice
     */
    private void carQuotedPriceSendMessageToTransportUser(TransportMainDO transportMain, Integer price,
                                                          boolean transportNowHaveNoPrice, boolean autoAgree) {
        UserRpcVO transportUser = userRemoteService.getUser(transportMain.getUserId());
        if (Objects.isNull(transportUser) || StringUtils.isBlank(transportUser.getCellPhone())) {
            return;
        }

        UserRpcVO carUser = userRemoteService.getUser(transportMain.getUserId());
        String firstName = carUser.getTrueName() == null ? "" : carUser.getTrueName().substring(0, 1);
        if (StringUtils.isBlank(firstName)) {
            firstName = carUser.getUserName() == null ? "" : carUser.getTrueName().substring(0, 1);
        }

        String content = "您发布的到" + transportMain.getDestCity() + "的货源，司机" + firstName + "师傅" + price + "元可走，快去查看>>";
        String title = "司机出价" + price + "元";

        MessagePushBase messagePushBase = new MessagePushBase();
        //添加推送用户
        messagePushBase.addUserId(transportMain.getUserId());
        messagePushBase.setTitle(title);
        messagePushBase.setContent(content);
        messagePushBase.setRemarks("司机出价" + price + "元");
        messagePushBase.setGoodsPush((short)1);

        //站内信
        messagePushBase.setContent("您" + transportMain.getStartCity() + "-" + transportMain.getDestCity() + "的货源，司机出价" + price + "元，请至货源详情查看");
        NewsMessagePush newsMessage = NewsMessagePush.createByPushBase(messagePushBase);

        NotifyMessagePush notifyMessage = null;
        ShortMessageBean shortMessage = null;
        if (!autoAgree) {
            //push
            notifyMessage = NotifyMessagePush.createByPushBase(messagePushBase);
            notifyMessage.openWithNativePage(NativePageEnum.goods_detail);
            notifyMessage.addNativeParameter("id", transportMain.getSrcMsgId().toString());

            Integer messageNumRule = tytConfigRemoteService.getIntValue("transport_receive_quoted_price_message_num_rule", 5);

            //短信
            // 获取当前时间
            java.time.LocalDateTime now = java.time.LocalDateTime.now();
            // 获取明天零点时间
            java.time.LocalDateTime midnight = now.plusDays(1).toLocalDate().atStartOfDay();
            // 计算当前时间到明天零点的秒差
            Duration duration = Duration.between(now, midnight);
            int secondsUntilMidnight = (int) duration.getSeconds();
            Long srcMsgId = transportMain.getSrcMsgId();
            Long transportUserId = transportMain.getUserId();
            Boolean carHasKey = stringRedisTemplate.hasKey(TRANSPORT_CAR_QUOTED_PRICE_MESSAGE_KEY + ":" + srcMsgId);
            if (!(Objects.nonNull(carHasKey) && carHasKey)) {
                Boolean transportHasKey = stringRedisTemplate.hasKey(TRANSPORT_CAR_QUOTED_PRICE_TRANSPORT_MESSAGE_KEY + ":" + transportUserId);
                if (Objects.nonNull(transportHasKey) && transportHasKey) {
                    String transportReceiveQuotedPriceMessageNum = stringRedisTemplate.opsForValue().get(TRANSPORT_CAR_QUOTED_PRICE_TRANSPORT_MESSAGE_KEY + ":" + transportUserId);
                    if (StringUtils.isNotBlank(transportReceiveQuotedPriceMessageNum) &&
                            Integer.parseInt(transportReceiveQuotedPriceMessageNum) < messageNumRule) {
                        shortMessage = new ShortMessageBean();
                        shortMessage.setCellPhone(transportUser.getCellPhone());
                        String url = tytConfigRemoteService.getStringValue("tyt_server_picture_url", "http://www.teyuntong.net");
                        shortMessage.setContent(content + url + "/jump.html?t=g&jp=cjjl");
                        shortMessage.setRemark("司机出价" + price + "元");
                        int num = Integer.parseInt(transportReceiveQuotedPriceMessageNum) + 1;
                        stringRedisTemplate.opsForValue().set(TRANSPORT_CAR_QUOTED_PRICE_TRANSPORT_MESSAGE_KEY + ":" + transportUserId, String.valueOf(num), secondsUntilMidnight, TimeUnit.SECONDS);
                    }
                } else {
                    shortMessage = new ShortMessageBean();
                    shortMessage.setCellPhone(transportUser.getCellPhone());
                    String url = tytConfigRemoteService.getStringValue("tyt_server_picture_url", "http://www.teyuntong.net");
                    shortMessage.setContent(content + url + "/jump.html?t=g&jp=cjjl");
                    shortMessage.setRemark("司机出价" + price + "元");
                    stringRedisTemplate.opsForValue().set(TRANSPORT_CAR_QUOTED_PRICE_TRANSPORT_MESSAGE_KEY + ":" + transportUserId, "1", secondsUntilMidnight, TimeUnit.SECONDS);
                }
                stringRedisTemplate.opsForValue().set(TRANSPORT_CAR_QUOTED_PRICE_MESSAGE_KEY + ":" + srcMsgId, "1", 60 * 60 * 30, TimeUnit.SECONDS);
            }
        }

        messageCenterService.sendMultiMessage(shortMessage, newsMessage, notifyMessage);
    }

    private void recordCarQuotedPrice(TransportMainDO transportMain) {
        Boolean hasUserKey = stringRedisTemplate.hasKey(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + transportMain.getUserId());
        if (hasUserKey) {
            Boolean hasSrcMsgIdKey = stringRedisTemplate.opsForHash().hasKey(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + transportMain.getUserId(), transportMain.getSrcMsgId().toString());
            if (hasSrcMsgIdKey) {
                Object o = stringRedisTemplate.opsForHash().get(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + transportMain.getUserId(), transportMain.getSrcMsgId().toString());
                int newCount = 1;
                if (Objects.nonNull(o)) {
                    newCount = Integer.parseInt(o.toString()) + 1;
                }
                stringRedisTemplate.opsForHash().put(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + transportMain.getUserId(), transportMain.getSrcMsgId().toString(), String.valueOf(newCount));
            } else {
                stringRedisTemplate.opsForHash().put(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + transportMain.getUserId(), transportMain.getSrcMsgId().toString(), "1");
            }
        } else {
            long tomorrowZeroSeconds = TimeUtil.getTomorrowZeroSeconds();
            stringRedisTemplate.opsForHash().put(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + transportMain.getUserId(), transportMain.getSrcMsgId().toString(), "1");
            stringRedisTemplate.expire(TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + transportMain.getUserId(), tomorrowZeroSeconds, TimeUnit.SECONDS);
        }
    }

    private Long InsertTransportQuotedPrice(QuotedPriceDTO priceDTO, TransportMainDO main, UserRpcVO carUser) {
        TransportQuotedPriceDO tytTransportQuotedPrice = new TransportQuotedPriceDO();
        tytTransportQuotedPrice.setCarId(priceDTO.getUserId());
        tytTransportQuotedPrice.setCarUserName(carUser.getCarUserName() == null ? carUser.getUserName() : carUser.getCarUserName());
        tytTransportQuotedPrice.setTransportUserId(main.getUserId());
        tytTransportQuotedPrice.setTransportUserName(main.getNickName());
        tytTransportQuotedPrice.setSrcMsgId(main.getSrcMsgId());
        tytTransportQuotedPrice.setCarQuotedPrice(priceDTO.getPrice());
        tytTransportQuotedPrice.setReason(priceDTO.getReason());
        transportQuotedPriceMapper.firstCarQuotedPriceV2(tytTransportQuotedPrice);
        return tytTransportQuotedPrice.getId();
    }

    /**
     * 校验运费
     *
     * @param transportMain
     * @param userId
     * @param transportQuotedPriceId
     * @param price
     */
    private void checkCarryPriceIsRules(TransportMainDO transportMain, Long userId, Long transportQuotedPriceId, Integer price) {
        int minPrice = 100;
        int maxPrice = 999999;

        TransportCarryReq carryPriceReq = buildTransportCarryReq(transportMain, userId);
        CarryPriceVO carryPriceVO = thPriceService.getThPrice(carryPriceReq);

        if (carryPriceVO != null) {
            Integer thMinPrice = carryPriceVO.getThMinPrice();
            Integer thMaxPrice = carryPriceVO.getThMaxPrice();
            if(thMinPrice != null && thMinPrice > minPrice){
                minPrice = thMinPrice;
            }
            if(thMaxPrice != null && thMaxPrice < maxPrice){
                maxPrice = thMaxPrice;
            }
        }

        // 保存bi出价日志
        saveQuotedPriceBiData(transportMain, userId, transportQuotedPriceId, price, carryPriceVO);

        if (price > maxPrice) {
            throw new BusinessException(QUOTED_PRICE_TOO_HIGH, null);
        }
        if (price < minPrice) {
            throw new BusinessException(QUOTED_PRICE_TOO_LOW, null);
        }
    }

    private void saveQuotedPriceBiData(TransportMainDO transportMain, Long userId, Long transportQuotedPriceId, Integer price, CarryPriceVO carryPriceVO) {
        QuotedPriceBiDataDO biDataDO = new QuotedPriceBiDataDO();
        biDataDO.setSrcMsgId(transportMain.getSrcMsgId());
        biDataDO.setQuotedPrice(price);
        biDataDO.setCarryPrice(Objects.isNull(carryPriceVO) ? "" : JSONObject.toJSONString(carryPriceVO));
        biDataDO.setCreateTime(new Date());
        if (Objects.isNull(transportQuotedPriceId)) {
            biDataDO.setQuotedType(1);
            biDataDO.setCarUserId(userId);
        } else {
            biDataDO.setQuotedType(2);
            TransportQuotedPriceDO transportQuotedPriceDO = transportQuotedPriceMapper.selectById(transportQuotedPriceId);
            if (Objects.nonNull(transportQuotedPriceDO)) {
                biDataDO.setCarUserId(transportQuotedPriceDO.getCarId());
            }
        }
        quotedPriceBiDataService.addQuotedPriceBiData(biDataDO);
    }

    @NotNull
    private static TransportCarryReq buildTransportCarryReq(TransportMainDO transportMain, Long userId) {
        TransportCarryReq carryPriceReq = new TransportCarryReq();
        carryPriceReq.setStartProvince(transportMain.getStartProvinc());
        carryPriceReq.setStartCity(transportMain.getStartCity());
        carryPriceReq.setStartArea(transportMain.getStartArea());
        carryPriceReq.setDestProvince(transportMain.getDestProvinc());
        carryPriceReq.setDestCity(transportMain.getDestCity());
        carryPriceReq.setDestArea(transportMain.getDestArea());
        carryPriceReq.setGoodsName(transportMain.getTaskContent());
        carryPriceReq.setGoodsWeight(new BigDecimal(transportMain.getWeight()));
        carryPriceReq.setGoodsLength(transportMain.getLength());
        carryPriceReq.setGoodsWide(transportMain.getWide());
        carryPriceReq.setGoodsHigh(transportMain.getHigh());
        carryPriceReq.setUserId(userId);
        carryPriceReq.setDistance(transportMain.getDistance());
        carryPriceReq.setExcellentGoods(transportMain.getExcellentGoods());
        carryPriceReq.setGoodTypeName(transportMain.getGoodTypeName());
        return carryPriceReq;
    }

    /**
     * 货主同意报价
     *
     * @param agreeDTO
     */
    @Override
    public QuotedPriceResultVO transportAgree(QuotedPriceDTO agreeDTO) {
        QuotedPriceResultVO result = new QuotedPriceResultVO();
        Boolean setSuccess = stringRedisTemplate.opsForValue()
                .setIfAbsent(TRANSPORT_QUOTED_PRICE_LOCK + ":" + agreeDTO.getUserId() + ":" + agreeDTO.getTransportQuotedPriceId(), "1", 5, TimeUnit.SECONDS);
        if (Objects.isNull(setSuccess) || !setSuccess) {
            throw new BusinessException(TRY_AGAIN_LATER, null);
        }

        TransportQuotedPriceDO quotedPriceDO = transportQuotedPriceMapper.selectById(agreeDTO.getTransportQuotedPriceId());
        if (Objects.isNull(quotedPriceDO)) {
            throw new BusinessException(NO_QUOTED_PRICE, null);
        }
        // 该车主对该货源的报价已被接受
        if (Objects.equals(quotedPriceDO.getFinalQuotedPriceIsDone(), 1)) {
            throw new BusinessException(QUOTED_PRICE_ACCEPTED, null);
        }
        // 数据错误，车方报价为空，货方不能同意
        if (Objects.isNull(quotedPriceDO.getCarQuotedPrice())) {
            throw new BusinessException(ERROR_NO_PARAM, null);
        }
        Integer autoAgree = agreeDTO.getAutoAgree();
        int priceAssistantAutoAgree = Objects.nonNull(autoAgree) && (autoAgree == 1 || autoAgree == 2) ? 1 : 0;
        transportQuotedPriceMapper.transportAgree(agreeDTO.getTransportQuotedPriceId(), priceAssistantAutoAgree);

        boolean addPrice = false;
        TransportMainDO transportMain = transportMainService.getTransportMainForId(agreeDTO.getSrcMsgId());
        if (StringUtils.isNotBlank(transportMain.getPrice()) &&
                new BigDecimal(quotedPriceDO.getCarQuotedPrice()).compareTo(new BigDecimal(transportMain.getPrice())) > 0) {
            addPrice = true;
        } else if (StringUtils.isBlank(transportMain.getPrice()) || new BigDecimal(transportMain.getPrice()).compareTo(BigDecimal.ZERO) == 0) {
            addPrice = true;
        }

        // 修改货源价格，重新计算技术服务费
        boolean addPriceIsSuccess = updatePriceAndTecServiceFee(agreeDTO, quotedPriceDO.getCarQuotedPrice(), transportMain, 2);

        // 同意报价发送push
        transportAgreeSendPush(quotedPriceDO, transportMain, addPrice, addPriceIsSuccess, CARGO_OWNER_REPLY, autoAgree);

        // 自动外呼联系车方
        addAutoCallTask(quotedPriceDO, transportMain, autoAgree);
        stringRedisTemplate.opsForValue().set(TRANSPORT_QUOTED_PRICE_CAR_NO_LOOK_HASH_KEY + ":" + quotedPriceDO.getCarId() + ":" + quotedPriceDO.getSrcMsgId(), "1", 60 * 60 * 30, TimeUnit.SECONDS);
        return result;
    }

    /**
     * 修改货源价格并重新计算技术服务费
     * 走货源加价逻辑
     *
     * @param agreeDTO          请求参数实体
     * @param quotedPrice       出价金额
     * @param main              货源实体
     * @param type              1-货主回价，2-货主同意报价
     * @return
     */
    private boolean updatePriceAndTecServiceFee(QuotedPriceDTO agreeDTO, Integer quotedPrice, TransportMainDO main, int type) {
        //车方报价和原有货源运费相等，则不做任何操作
        if (StringUtils.isNotBlank(main.getPrice()) && Integer.parseInt(main.getPrice()) == quotedPrice) {
            return false;
        }
        // 开票货源或非灵活运价的专车货源不支持修改运费
        Integer priceType = transportMainExtendMapper.getPriceTypeBySrcMsgId(agreeDTO.getSrcMsgId());
        if (Objects.equals(main.getInvoiceTransport(), YesOrNoEnum.YES.getId()) ||
                (Objects.equals(main.getExcellentGoods(), ExcellentGoodsEnums.SPECIAL.getCode()) &&
                        !Objects.equals(priceType, PriceTypeEnum.FLEXIBLE.getCode()))) {
            return false;
        }

        String price = main.getPrice();
        // 更新货源价格
        DirectPublishDTO directPublishDTO = new DirectPublishDTO();
        directPublishDTO.setSrcMsgId(main.getSrcMsgId());
        directPublishDTO.setPrice(quotedPrice.toString());
        transportDirectPublishRpcService.updatePrice(directPublishDTO);

        if (Objects.equals(type, 1)) {
            BigDecimal beforePrice = StringUtils.isBlank(price) || new BigDecimal(price).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : new BigDecimal(price);
            quotedModifyPriceLogService.addModifyPriceLog(agreeDTO.getTransportQuotedPriceId(), agreeDTO.getSrcMsgId(), beforePrice, new BigDecimal(quotedPrice));
        }

        return true;
    }

    /**
     * 添加自动外呼任务
     */
    private void addAutoCallTask(TransportQuotedPriceDO tytTransportQuotedPrice, TransportMainDO transportMain, Integer autoAgree) {
        Integer autoCallOnOff = tytConfigRemoteService.getIntValue("agree_quoted_price_auto_call_on_off", 0);
        if (autoCallOnOff == 1 && (Objects.isNull(autoAgree) || autoAgree == 2)) {
            try {
                //货方同意报价自动外呼联系车方
                AutoCallTaskRequest autoCallTaskRequest = new AutoCallTaskRequest();
                autoCallTaskRequest.setTaskName("同意报价-" + tytTransportQuotedPrice.getSrcMsgId() + "-" + tytTransportQuotedPrice.getCarId());

                UserRpcVO user = userRemoteService.getUser(tytTransportQuotedPrice.getCarId());
                if (user != null && StringUtils.isNotBlank(user.getCellPhone())) {
                    List<String> cellPhoneList = new ArrayList<>();
                    cellPhoneList.add(user.getCellPhone());
                    autoCallTaskRequest.setCallTelList(cellPhoneList);

                    if (transportMain != null && StringUtils.isNotBlank(transportMain.getStartCity()) && StringUtils.isNotBlank(transportMain.getDestCity())) {
                        autoCallTaskRequest.setTaskCallValue("您报价的" + transportMain.getStartCity() + "到"
                                + transportMain.getDestCity() + "的货源已被货主采纳，价格为"
                                + Convert.numberToChinese(tytTransportQuotedPrice.getCarQuotedPrice(), false)
                                + "元，快来抢单吧");
                        autoCallTaskRemoteService.autoCallTask(autoCallTaskRequest);
                    }
                }
            } catch (Exception e) {
                log.info("同意报价创建自动外呼失败，原因：", e);
            }
        }
    }


    /**
     * 货方同意报价发送push
     *
     * @param quotedPriceDO
     * @param transportMain
     * @param addPrice
     * @param addPriceIsSuccess
     * @param type
     * @param autoAgree  1-车主出价自动同意，2-回价助手任务设置自动同意
     */
    private void transportAgreeSendPush(TransportQuotedPriceDO quotedPriceDO, TransportMainDO transportMain,
                                        boolean addPrice, boolean addPriceIsSuccess, int type, Integer autoAgree) {
        try {
            if (Objects.equals(QuotedTypeEnum.SYSTEM.getCode(), quotedPriceDO.getQuotedType()) && addPrice && addPriceIsSuccess) {
                // 系统出价，货主同意时给查看过货源的司机推送、短信、站内信
                systemQuotedPricePush(quotedPriceDO, transportMain);
            } else {
                if (Objects.isNull(autoAgree) || autoAgree == 2) {
                    transportQuotedPriceChangeNeedPush(quotedPriceDO, CARGO_OWNER_AGREE, addPrice && addPriceIsSuccess, transportMain);
                }
            }
        } catch (Exception e) {
            log.info("货方同意报价报价通知车方push消息失败 报价表ID：{}", quotedPriceDO.getId());
        }
    }

    private void transportQuotedPriceChangeNeedPush(TransportQuotedPriceDO quotedPriceDO, int type, boolean addPrice, TransportMainDO transportMain) {
        try {
            Integer quotedPriceTotalTimes = getQuotedPriceTotalTimes();

            UserRpcVO carUser = userRemoteService.getUser(quotedPriceDO.getCarId());
            if (carUser != null && StringUtils.isNotEmpty(carUser.getCellPhone())) {
                String title;
                String content;
                String remark;
                if (type == 1) {
                    //货方回价
                    if (quotedPriceDO.getCarQuotedPriceTimes() >= quotedPriceTotalTimes) {
                        //车方出价次数超过配置的总次数，车方不能再进行出价
                        title = QUOTED_PRICE_NOT_AGREE_CAN_TEL;
                        content = QUOTED_PRICE_NOT_AGREE_CAN_TEL;
                        remark = QUOTED_PRICE_NOT_AGREE_CAN_TEL;
                    } else {
                        //车方可以再进行出价
                        title = QUOTED_PRICE_NOT_AGREE;
                        content = QUOTED_PRICE_NOT_AGREE;
                        remark = QUOTED_PRICE_NOT_AGREE;
                    }
                } else {
                    //货方同意报价
                    content = "货主已同意您报价的去往" + transportMain.getDestCity() + "的货源！快去接单吧>>";
                    title = content;
                    remark = content;
                }

                MessagePushBase messagePushBase = new MessagePushBase();
                //添加推送用户
                messagePushBase.addUserId(quotedPriceDO.getCarId());
                messagePushBase.setTitle(title);
                messagePushBase.setContent(content);
                messagePushBase.setRemarks(remark);
                messagePushBase.setCarPush((short)1);

                //push
                NotifyMessagePush notifyMessage = NotifyMessagePush.createByPushBase(messagePushBase);
                notifyMessage.openWithNativePage(NativePageEnum.goods_detail);
                notifyMessage.addNativeParameter("id", transportMain.getSrcMsgId().toString());

                //站内信
                NewsMessagePush newsMessage = NewsMessagePush.createByPushBase(messagePushBase);

                //短信
                ShortMessageBean shortMessage = new ShortMessageBean();
                shortMessage.setCellPhone(carUser.getCellPhone());
                //短信增加跳转货源详情链接
                String url = tytConfigRemoteService.getStringValue("tyt_server_picture_url", "http://www.teyuntong.net");
                shortMessage.setContent(content + " " + url + "/jump.html?t=c&jp=cgd&id=" + transportMain.getSrcMsgId() + "&viewSource=12");
                shortMessage.setRemark(title);

                messageCenterService.sendMultiMessage(shortMessage, newsMessage, notifyMessage);
            }

            //增加inpush
            transportQuotedPriceInPush(quotedPriceDO, transportMain , type, addPrice);
        } catch (Exception e) {
            log.error("transportQuotedPriceChangeNeedPush error:{}", quotedPriceDO, e);
        }
    }

    private void transportQuotedPriceInPush(TransportQuotedPriceDO quotedPriceDO, TransportMainDO transportMain, Integer type, boolean addPrice) {
        MessagePushBase messagePushBase = new MessagePushBase();
        //货方同意或拒绝报价通知报价车方
        messagePushBase.addUserId(quotedPriceDO.getCarId());

        JSONObject extraJson = new JSONObject();
        String startAddress = transportMain.getStartCity() + transportMain.getStartArea();
        String destAddress = transportMain.getDestCity() + transportMain.getDestArea();

        extraJson.put("srcMsgId", transportMain.getSrcMsgId());
        extraJson.put("price", transportMain.getPrice());
        extraJson.put("content", startAddress + "——" + destAddress);
        String goodsName = StringUtils.isBlank(transportMain.getGoodTypeName()) ? "货源" : transportMain.getGoodTypeName();
        extraJson.put("infoText", goodsName + " " + transportMain.getWeight() + "吨 ");
        extraJson.put("addPrice", addPrice);
        if (type == CARGO_OWNER_REPLY) {
            //货方回价
            messagePushBase.setTitle("货主拒绝了您的报价，您可再次出价");
            messagePushBase.setContent("货主拒绝了您的报价，您可再次出价");
            messagePushBase.setRemarks("货主拒绝了您的报价，您可再次出价");
            extraJson.put("title", "货主拒绝了您的报价，您可再次出价");
        } else {
            //货方同意
            messagePushBase.setTitle("货主同意了您的报价");
            messagePushBase.setContent("货主同意了您的报价");
            messagePushBase.setRemarks("货主同意了您的报价");
            extraJson.put("title", "货主同意了您的报价");
        }
        messagePushBase.setCarPush((short)1);
        NotifyMessagePush notifyMessage = NotifyMessagePush.createByPushBase(messagePushBase);
        notifyMessage.setPushCode(PushCodeEnum.RECOMMEND_GOODS_PUSH.getCode());
        notifyMessage.setExtraData(extraJson.toString());
        log.info("发送报价inpush {}", JSON.toJSONString(notifyMessage));
        messageCenterService.sendMultiMessage(null, null, notifyMessage);

        if (addPrice) {
            List<Long> viewLogUserIdListBySrcMsgId = transportDispatchViewDetailService.queryViewCarUser(transportMain.getSrcMsgId());
            if (CollectionUtils.isNotEmpty(viewLogUserIdListBySrcMsgId)) {
                //排除掉报价司机
                viewLogUserIdListBySrcMsgId.remove(quotedPriceDO.getCarId());
                //货源因为货主同意或拒绝报价修改了运费，并且运费涨价，则通知所有查看过该货源的车主
                transportQuotedPriceInPushAllViewUser(transportMain, viewLogUserIdListBySrcMsgId);
            }
        }
    }

    /**
     * 系统报价，货主同意给查看过货源的司机推送
     *
     * @param quotedPriceDO
     * @param transportMain
     */
    private void systemQuotedPricePush(TransportQuotedPriceDO quotedPriceDO, TransportMainDO transportMain) {
        List<Long> carUserIds = transportDispatchViewDetailService.queryViewCarUser(quotedPriceDO.getSrcMsgId());
        if (CollectionUtils.isEmpty(carUserIds)) {
            return;
        }

        carUserIds.remove(quotedPriceDO.getCarId());

        transportQuotedPriceInPushAllViewUser(transportMain, carUserIds);
    }

    private void transportQuotedPriceInPushAllViewUser(TransportMainDO transportMain, List<Long> viewLogUserIdListBySrcMsgId) {
        if (CollectionUtils.isEmpty(viewLogUserIdListBySrcMsgId)) {
            return;
        }
        String startAddress = transportMain.getStartCity() + transportMain.getStartArea();
        String destAddress = transportMain.getDestCity() + transportMain.getDestArea();

        GoodsPushDto pushDto = new GoodsPushDto();
        pushDto.setSrcMsgId(transportMain.getId());
        pushDto.setPushSource("货源报价推送");
        pushDto.setTitle("货主上调了运费");
        String goodsName = StringUtils.isBlank(transportMain.getGoodTypeName()) ? "货源" : transportMain.getGoodTypeName();
        pushDto.setContent("货主上调了运费");
        pushDto.setUserIdList(viewLogUserIdListBySrcMsgId);
        pushDto.setPushType(PushTypeEnum.ALL_PUSH);
        pushDto.setPushCode(PushCodeEnum.RECOMMEND_GOODS_PUSH);
        pushDto.setSrcMsgId(transportMain.getSrcMsgId());
        JSONObject extraJson = new JSONObject();
        extraJson.put("srcMsgId", transportMain.getSrcMsgId());
        extraJson.put("price", transportMain.getPrice());
        extraJson.put("title", "货主上调了运费");
        extraJson.put("content", startAddress + "——" + destAddress);
        extraJson.put("infoText", goodsName + " " + transportMain.getWeight() + "吨 ");
        extraJson.put("addPrice", true);
        pushDto.setExtraJson(extraJson.toString());
        log.info("发送所有浏览过该货源的司机报价inpush {}", JSON.toJSONString(pushDto));

        try {
            goodsPushRemoteService.sendPush(pushDto);
        } catch (Exception e) {
            log.info("发送所有浏览过该货源的司机报价inpush失败原因：", e);
        }
    }

    /**
     * 是否可以出价/展示出价记录判断
     * 不校验一口价和无价
     *
     * @param srcMsgId
     * @return
     */
    // @Override
    // public CheckTransportValidityVO checkTransportValidity(Long srcMsgId) {
    //     TransportMainDO transportMain = transportMainService.getTransportMainForId(srcMsgId);
    //     if (transportMain != null && transportMain.getStatus() != null && transportMain.getPubDate() != null) {
    //         if ((!Objects.equals(TransportStatusEnum.VALID.getCode(), transportMain.getStatus())) ||
    //                 !TimeUtil.isToday(transportMain.getPubDate().getTime())) {
    //             //状态为无效
    //             return CheckTransportValidityVO.failResponse(10001, "货源已失效");
    //         }
    //
    //         boolean isHavePriceRuleTransport = false;
    //         if (transportMain.getPublishType() == 2) {
    //             //车方查看一口价货源是判断是否可以看到报价相关组件，非一口价货源由app来判断
    //             Integer priceType = transportMainExtendMapper.getPriceTypeBySrcMsgId(srcMsgId);
    //             if ((transportMain.getInvoiceTransport() == null || transportMain.getInvoiceTransport() != 1)
    //                     && (transportMain.getExcellentGoods() == null || transportMain.getExcellentGoods() != 2 || (priceType != null && priceType == 2))) {
    //                 isHavePriceRuleTransport = true;
    //             }
    //         }
    //
    //         if (StringUtils.isNotBlank(transportMain.getPrice()) && !isHavePriceRuleTransport) {
    //             return CheckTransportValidityVO.failResponse(10001, "该货源不允许出价");
    //         }
    //     }
    //     return CheckTransportValidityVO.successResponse();
    // }


    /**
     * 货获取某个货源的所有报价列表
     *
     * @param srcMsgId
     * @param userId
     * @return
     */
    @Override
    public List<TransportQuotedPriceTransportVO> getTransportQuotedPriceList(Long srcMsgId, Long userId) {
        boolean isOwner = transportMainService.checkUserIsTransportOwner(userId, srcMsgId);
        if (!isOwner) {
            throw new BusinessException(ERROR_NO_PARAM, null);
        }

        List<TransportQuotedPriceTransportVO> result = getTransportQuotedPriceTransportVOList(srcMsgId);

        makeCarUserData(result);

        if (!result.isEmpty()) {
            for (TransportQuotedPriceTransportVO transportQuotedPriceTransportVO : result) {
                if (transportQuotedPriceTransportVO.getQuotedType() != null && transportQuotedPriceTransportVO.getQuotedType() == 0
                        && transportQuotedPriceTransportVO.getFinalQuotedPriceIsDone() != null && transportQuotedPriceTransportVO.getFinalQuotedPriceIsDone() == 1) {
                    transportQuotedPriceTransportVO.setHaveCallToCarButton(1);
                }
            }
        }

        // 货主查看某个货源的报价列表
        if (Objects.nonNull(redisUtil.hashGet(RedisKeyConstant.TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + userId, srcMsgId.toString()))) {
            // 将该货源的货主未浏览报价次数清空
            redisUtil.hashDelete(RedisKeyConstant.TRANSPORT_QUOTED_PRICE_COUNT_HASH_KEY + ":" + userId, srcMsgId.toString());
        }

        return result;
    }

    private void makeCarUserData(List<TransportQuotedPriceTransportVO> result) {
        try {
            if (CollectionUtils.isNotEmpty(result)) {
                String tytServerPictureUrl = tytConfigRemoteService.getStringValue("tyt_server_picture_url_old", "http://www.teyuntong.com/rootdata");

                for (TransportQuotedPriceTransportVO transportQuotedPriceTransportVO : result) {
                    fillSingleVO(tytServerPictureUrl, transportQuotedPriceTransportVO);
                }
            }
        } catch (Exception e) {
            log.error("makeCarUserData error:", e);
        }
    }

    /**
     * 补充车主信息
     *
     * @param tytServerPictureUrl
     * @param transportQuotedPriceTransportVO
     */
    private void fillSingleVO(String tytServerPictureUrl, TransportQuotedPriceTransportVO transportQuotedPriceTransportVO) throws Exception {
        // 返回总交易数、与我交易数
        List<CreditUserRpcVO> creditUserInfos = userRemoteService.getPayUserInfos(Collections.singletonList(transportQuotedPriceTransportVO.getCarId()), transportQuotedPriceTransportVO.getTransportUserId());
        if (CollectionUtils.isNotEmpty(creditUserInfos)) {
            CreditUserRpcVO creditUserInfo = creditUserInfos.get(0);
            transportQuotedPriceTransportVO.setCoopNums(creditUserInfo.getCoopNums());
            transportQuotedPriceTransportVO.setTradeNums(creditUserInfo.getTradeNums());
        }

        UserRpcVO user = userRemoteService.getUser(transportQuotedPriceTransportVO.getCarId());
        if (user != null) {
            // 返回拼好的头像
            if (org.apache.commons.lang3.StringUtils.isNotBlank(user.getHeadUrl())) {
                String pattern = "^(http|https)";
                Pattern compiledPattern = Pattern.compile(pattern);
                Matcher matcher = compiledPattern.matcher(user.getHeadUrl());
                if (!matcher.find()) {
                    transportQuotedPriceTransportVO.setHeadUrl(tytServerPictureUrl + user.getHeadUrl());
                } else {
                    transportQuotedPriceTransportVO.setHeadUrl(user.getHeadUrl());
                }
            }

            ApiDataUserCreditInfoRpcVO apiDataUserCreditInfoTwo = userRemoteService.getUserCreditInfo(user.getId());
            if (apiDataUserCreditInfoTwo != null) {
                transportQuotedPriceTransportVO.setCarCreditRankLevel(apiDataUserCreditInfoTwo.getCarCreditRankLevel());
            }
            // 查询车方的好评率和标签
            UserFeedbackRatingAndLabelDTO userFeedbackRatingAndLabel = feedBackRemoteService.getUserFeedbackRatingAndLabel(user.getId(), 1);
            if(userFeedbackRatingAndLabel != null){
                transportQuotedPriceTransportVO.setRating(userFeedbackRatingAndLabel.getRating());
                transportQuotedPriceTransportVO.setPositiveCount(userFeedbackRatingAndLabel.getPositiveCount());
                transportQuotedPriceTransportVO.setPositiveLabels(userFeedbackRatingAndLabel.getPositiveLabels());
                transportQuotedPriceTransportVO.setNegativeLabels(userFeedbackRatingAndLabel.getNegativeLabels());
                // 30天保护期
                if (DateUtils.addDays(user.getCtime() == null ? new Date() : user.getCtime(), 30).before(new Date())) {
                    // 总评价量
                    Long total = userFeedbackRatingAndLabel.getTotal();
                    // 好评数
                    Long positiveCount = userFeedbackRatingAndLabel.getPositiveCount();
                    if (total == 0) {
                        transportQuotedPriceTransportVO.setRatingType(RatingTypeEnum.RECENT_NO_RATE.getCode());
                    } else if (total > 0 && total < 3) {
                        // 有好评
                        if(positiveCount > 0){
                            transportQuotedPriceTransportVO.setRatingType(RatingTypeEnum.RECENT_RECEIVE_POSITIVE.getCode());
                        } else { // 无好评
                            transportQuotedPriceTransportVO.setRatingType(RatingTypeEnum.RECENT_NOT_RECEIVE_POSITIVE.getCode());
                        }
                    } else if (total >= 3) {
                        transportQuotedPriceTransportVO.setRatingType(RatingTypeEnum.SHOW_RATING.getCode());
                    }
                } else {
                    transportQuotedPriceTransportVO.setRating(null);
                    transportQuotedPriceTransportVO.setRatingType(RatingTypeEnum.NOT_SHOW_RATING.getCode());
                    transportQuotedPriceTransportVO.setNegativeLabels(Collections.emptyList());
                }

            }

            // 车是否会员
            UserPermissionRpcDTO permissionRpcDTO = new UserPermissionRpcDTO();
            permissionRpcDTO.setServicePermissionTypeId(UserPermissionTypeEnum.CAR_VIP.getTypeId());
            permissionRpcDTO.setUserId(transportQuotedPriceTransportVO.getCarId());
            permissionRpcDTO.setStatus(UserPermissionStatusEnum.EFFECTIVE.getStatus());
            List<UserPermissionRpcVO> userPermission = userPermissionRemoteService.getUserPermissionByUserId(permissionRpcDTO);
            transportQuotedPriceTransportVO.setCarIsVip(CollectionUtils.isNotEmpty(userPermission) ? 1 : 0);

            // 返回是否实名认证
            transportQuotedPriceTransportVO.setRealNameAuthentication(Objects.equals(user.getVerifyPhotoSign(), 1));
        }
    }

    public List<TransportQuotedPriceTransportVO> getTransportQuotedPriceTransportVOList(Long srcMsgId) {
        List<TransportQuotedPriceTransportVO> result = new ArrayList<>();

        // 获取配置的最大报价总次数，如果配置不存在或者小于1，则直接返回1
        Integer quotedPriceTotalTimes = getQuotedPriceTotalTimes();

        // 如果货源不是无价货源或者优车2.0货源或者有效货源，直接返回空
        if (!checkTransportValidityV2(srcMsgId, false)) {
            return new ArrayList<>();
        }

        List<TransportQuotedPriceDO> quotedPriceList = transportQuotedPriceMapper.getQuotedPriceListByTransportMainId(srcMsgId);
        if (CollectionUtils.isEmpty(quotedPriceList)) {
            return new ArrayList<>();
        }
        TransportMainDO main = transportMainService.getTransportMainForId(srcMsgId);
        for (TransportQuotedPriceDO tytTransportQuotedPrice : quotedPriceList) {
            TransportQuotedPriceTransportVO transportQuotedPriceTransportVO = new TransportQuotedPriceTransportVO();
            BeanUtils.copyProperties(tytTransportQuotedPrice, transportQuotedPriceTransportVO);
            transportQuotedPriceTransportVO.setPrice(main.getPrice());
            transportQuotedPriceTransportVO.setGoodsTypeName(main.getGoodTypeName());
            Integer goodsTypeNum = dwsGoodTypeCntService.getGoodsTypeCnt(tytTransportQuotedPrice.getCarId(), main.getGoodTypeName());
            transportQuotedPriceTransportVO.setGoodsTypeNum(goodsTypeNum);
            result.add(transportQuotedPriceTransportVO);

            // 报价已被接受
            if (transportQuotedPriceTransportVO.getFinalQuotedPriceIsDone() == 1) {
                continue;
            }

            // 车方未响应
            if (transportQuotedPriceTransportVO.getCarQuotedPriceTimes().equals(transportQuotedPriceTransportVO.getTransportQuotedPriceTimes())) {
                transportQuotedPriceTransportVO.setCarIsQuotedPriceAgain(false);
                continue;
            }
            transportQuotedPriceTransportVO.setCarIsQuotedPriceAgain(true);

            // 货方出价次数用完，不能再进行出价
            if (transportQuotedPriceTransportVO.getTransportQuotedPriceTimes() >= quotedPriceTotalTimes) {
                transportQuotedPriceTransportVO.setCanQuotedPrice(false);
                continue;
            }
            transportQuotedPriceTransportVO.setCanQuotedPrice(true);
        }
        return result;
    }


}
