package com.teyuntong.goods.service.service.biz.goodsname.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 存储无效关键字
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-05-10
 */
@Getter
@Setter
@TableName("tyt_nullify_keyword")
public class NullifyKeywordDO {

    /**
     * 关键字ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关键字类别 0货物1敏感词
     */
    private Integer keywordType;

    /**
     * 关键字内容
     */
    private String keywordValue;

    private Date ctime;

    private Date mtime;
}
