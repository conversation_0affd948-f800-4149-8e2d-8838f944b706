package com.teyuntong.goods.service.service.mq.hooks;

import com.aliyun.openservices.ons.api.OnExceptionContext;
import com.aliyun.openservices.ons.api.SendResult;
import com.teyuntong.infra.common.rocketmq.hooks.MessageProduceHook;
import com.teyuntong.infra.common.rocketmq.message.MqMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/11/08 10:01
 */
@Component
public class LogMsgProducerHook implements MessageProduceHook {

    /**
     * 需要定义一个 name 为 mq_producer 的 logger 来指定日志的行为
     */
    private static final Logger LOGGER = LoggerFactory.getLogger("mq_producer");

    @Override
    public void onSendNormalSuccess(MqMessage message, SendResult sendResult) {
        LOGGER.info("RocketMq sendNormal 成功, message: {}, result: {}", message, sendResult);
    }

    @Override
    public void onSendNormalFailure(MqMessage message, Exception e) {
        LOGGER.error("RocketMq sendNormal 失败, message: {}", message, e);
    }

    @Override
    public void onSendAsyncSuccess(MqMessage message, SendResult sendResult) {
        LOGGER.info("RocketMq sendNormalAsync 成功, message: {}, result: {}", message, sendResult);
    }

    @Override
    public void onSendAsyncFailure(MqMessage message, OnExceptionContext context) {
        LOGGER.error("RocketMq sendNormalAsync 失败, message: {}", message, context.getException());
    }

    @Override
    public void onSendNormalOnewaySuccess(MqMessage message) {
        LOGGER.info("RocketMq sendOneway 成功, 但是不保证服务器一定能收到消息, message: {}", message);
    }

    @Override
    public void onSendNormalOnewayFailure(MqMessage message, Exception e) {
        LOGGER.error("RocketMq sendOneway 失败, message: {}", message, e);
    }

    @Override
    public void onSendTransactionSuccess(MqMessage message, Object arg, SendResult sendResult) {
        LOGGER.info("RocketMq sendTransaction 成功, message: {}, arg: {}, result: {}", message, arg, sendResult);
    }

    @Override
    public void onSendTransactionFailure(MqMessage message, Object arg, Exception e) {
        LOGGER.error("RocketMq sendTransaction 失败, arg: {}, message: {}", message, arg, e);
    }

    @Override
    public void onSendOrderSuccess(MqMessage message, String shardingKey, SendResult sendResult) {
        LOGGER.info("RocketMq sendOrdered 成功, message: {}, shardingKey: {}, result: {}",
                message, shardingKey, sendResult);
    }

    @Override
    public void onSendOrderFailure(MqMessage message, String shardingKey, Exception e) {
        LOGGER.error("RocketMq sendOrdered 失败, message: {}, shardingKey: {}", message, shardingKey, e);
    }
}
