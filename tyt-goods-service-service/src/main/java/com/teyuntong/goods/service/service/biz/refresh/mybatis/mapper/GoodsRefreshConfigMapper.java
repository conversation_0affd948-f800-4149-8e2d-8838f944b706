package com.teyuntong.goods.service.service.biz.refresh.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.service.client.transport.dto.GoodsRefreshConfigDTO;
import com.teyuntong.goods.service.service.biz.refresh.mybatis.entity.GoodsRefreshConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 货源刷新配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-04-12
 */
@Mapper
public interface GoodsRefreshConfigMapper extends BaseMapper<GoodsRefreshConfigDO> {
    /**
     * @return List<RefreshContentDto>
     * @Param userId:
     */
    List<GoodsRefreshConfigDTO> selectExcellentGoodsContentByUserId(@Param("userId") Long userId);
}
