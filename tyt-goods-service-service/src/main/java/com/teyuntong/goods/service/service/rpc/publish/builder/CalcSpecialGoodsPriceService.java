package com.teyuntong.goods.service.service.rpc.publish.builder;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.teyuntong.goods.service.client.publish.dto.CalcSpecialGoodsPriceResultDTO;
import com.teyuntong.goods.service.client.publish.dto.CalculatePriceDTO;
import com.teyuntong.goods.service.service.biz.publish.mybatis.entity.TransportPricePerkConfigDO;
import com.teyuntong.goods.service.service.biz.publish.service.TransportPricePerkConfigService;
import com.teyuntong.goods.service.service.biz.transport.dto.PriceConfigBean;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchCargoOwnerDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchCompanyDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchCooperativeDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.SpecialCarPriceConfigDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.SpecialCarPriceConfigMapper;
import com.teyuntong.goods.service.service.biz.transport.service.DispatchCargoOwnerService;
import com.teyuntong.goods.service.service.biz.transport.service.DispatchCompanyService;
import com.teyuntong.goods.service.service.biz.transport.service.DispatchCooperativeService;
import com.teyuntong.goods.service.service.biz.transport.service.SpecialCarPriceConfigService;
import com.teyuntong.goods.service.service.common.enums.DriverDrivingEnum;
import com.teyuntong.goods.service.service.common.enums.PriceConfigTypeEnum;
import com.teyuntong.goods.service.service.common.enums.PriceTypeEnum;
import com.teyuntong.goods.service.service.common.enums.UseCarTypeEnum;
import com.teyuntong.goods.service.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.service.service.remote.basic.TytCityRemoteService;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.infra.basic.resource.client.tytcity.vo.TytCityVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

import static com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant.SPECIAL_CAR_PRICE_CONFIG_TONNAGE;

/**
 * 计算专车运费service
 *
 * <AUTHOR>
 * @since 2025/02/18 15:15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CalcSpecialGoodsPriceService {

    private final TytConfigRemoteService tytConfigRemoteService;
    private final DispatchCompanyService dispatchCompanyService;
    private final TytCityRemoteService tytCityRemoteService;
    private final DispatchCargoOwnerService dispatchCargoOwnerService;
    private final SpecialCarPriceConfigService specialCarPriceConfigService;
    private final DispatchCooperativeService dispatchCooperativeService;
    private final SpecialCarPriceConfigMapper specialCarPriceConfigMapper;
    private final ABTestRemoteService abTestRemoteService;
    private final TransportPricePerkConfigService transportPricePerkConfigService;

    private static final String GOODS_PRICE_PERK_ABTEST = "goods_price_perk_abtest";

    /**
     * since 6480
     * 专车二期专线，运费测算
     *
     * @param priceDTO
     * @return
     */
    public CalcSpecialGoodsPriceResultDTO calculatePriceV2(CalculatePriceDTO priceDTO) {
        // 处理app传过来的地址和后台地址不匹配问题   (德宏傣族景颇族自治州 -> 德宏州)
        TytCityVo startCityVo = tytCityRemoteService.getShortCityName(priceDTO.getStartCity());
        if (Objects.nonNull(startCityVo)) {
            priceDTO.setStartCity(startCityVo.getCityName());
        }
        TytCityVo destCityVo = tytCityRemoteService.getShortCityName(priceDTO.getDestCity());
        if (Objects.nonNull(destCityVo)) {
            priceDTO.setDestCity(destCityVo.getCityName());
        }

        return doCalculatePriceV2(priceDTO);

    }

    /**
     * 执行专车价格测算
     *
     * @param priceDTO
     * @return
     */
    public CalcSpecialGoodsPriceResultDTO doCalculatePriceV2(CalculatePriceDTO priceDTO) {
        CalcSpecialGoodsPriceResultDTO resultDTO = new CalcSpecialGoodsPriceResultDTO();
        resultDTO.setPrice(BigDecimal.ZERO);
        Long cargoOwnerId = priceDTO.getCargoOwnerId();

        // 如果当前登录人是代调，可以跳过发货第一页运费测算
        if (dispatchCompanyService.countByUserId(priceDTO.getUserId()) > 0
                && (Objects.isNull(priceDTO.getCargoOwnerId()) || priceDTO.getCargoOwnerId() == 0)) {
            // 是代调账号，并且没有选择签约合作商
            resultDTO.setPrice(BigDecimal.ONE);

            // 没有签约合作商就一定是平台
            Integer fastDispatchMin = tytConfigRemoteService.getIntValue("special_car_dispatch_time_normal", 10);
            //专车最快接单时间
            resultDTO.setFastDispatchMin(fastDispatchMin);
            //专车最快接单时间文案
            resultDTO.setFastDispatchWord("最快" + fastDispatchMin + "分钟接单 不扣发货次数");
            return resultDTO;
        }

        // 是否是普通专车货主
        boolean isNormalCargoOwner = false;
        DispatchCargoOwnerDO owner = dispatchCargoOwnerService.selectSignedByUserId(priceDTO.getUserId());
        // 匹配签约合作商
        if (Objects.isNull(cargoOwnerId) || cargoOwnerId == 0) {
            if (Objects.nonNull(owner) && Objects.nonNull(owner.getCooperativeId()) && owner.getCooperativeId() != 0) {
                cargoOwnerId = owner.getCooperativeId();
            } else {
                DispatchCooperativeDO cooperative = dispatchCooperativeService.selectByName("平台");
                if (Objects.nonNull(cooperative)) {
                    cargoOwnerId = cooperative.getId();
                    isNormalCargoOwner = true;
                }
            }
            if (Objects.isNull(cargoOwnerId)) {
                log.info("专车运费计算未匹配到签约合作商，userId:{}, startCity:{}, destCity:{}, weight:{}",
                        priceDTO.getUserId(), priceDTO.getStartCity(), priceDTO.getDestCity(),
                        priceDTO.getWeight());
                resultDTO.setNotice("您当前路线暂不支持专车发布");
                return resultDTO;
            }
        } else {
            DispatchCooperativeDO cooperative = dispatchCooperativeService.getById(cargoOwnerId);
            if (Objects.nonNull(cooperative) && cooperative.getStatus() != 1) {
                // 签约合作商不是有效状态，取平台
                DispatchCooperativeDO plat = dispatchCooperativeService.selectByName("平台");
                if (Objects.isNull(plat)) {
                    log.info("专车运费计算未匹配到签约合作商，userId:{}, startCity:{}, destCity:{}, weight:{}",
                            priceDTO.getUserId(), priceDTO.getStartCity(), priceDTO.getDestCity(),
                            priceDTO.getWeight());
                    resultDTO.setNotice("您当前路线暂不支持专车发布");
                    return resultDTO;
                }
                cargoOwnerId = plat.getId();
                isNormalCargoOwner = true;
            }
        }

        Integer fastDispatchMin = tytConfigRemoteService.getIntValue("special_car_dispatch_time_normal", 10);
        int priceNoNormalCount = specialCarPriceConfigService.countMatchPriceConfigCityAndRule(priceDTO.getStartCity(), priceDTO.getDestCity());
        if (priceNoNormalCount > 0) {
            //如果出发地目的地在专车路线运费配置里面存在非平台的配置，则展示非平台X分钟
            fastDispatchMin = tytConfigRemoteService.getIntValue("special_car_dispatch_time_not_normal", 20);
        }

        //专车最快接单时间
        resultDTO.setFastDispatchMin(fastDispatchMin);
        //专车最快接单时间文案
        resultDTO.setFastDispatchWord("最快" + fastDispatchMin + "分钟接单 不扣发货次数");

        // 匹配运费规则
        BigDecimal weight = new BigDecimal(priceDTO.getWeight());
        SpecialCarPriceConfigDO priceConfig = specialCarPriceConfigService.selectMatchPriceConfig(cargoOwnerId,
                priceDTO.getStartCity(), priceDTO.getDestCity(), weight);
        if (Objects.isNull(priceConfig)) {
            log.info("专车运费计算未匹配到运费规则配置，userId:{}, startCity:{}, destCity:{}, weight:{}，cargoOwnerId:{}",
                    priceDTO.getUserId(), priceDTO.getStartCity(), priceDTO.getDestCity(),
                    priceDTO.getWeight(), cargoOwnerId);
            String notice = "您当前路线暂不支持专车发布";
            int count = specialCarPriceConfigService.countByOwnerAndRoute(cargoOwnerId, priceDTO.getStartCity(),
                    priceDTO.getDestCity());
            if (count > 0) {
                notice = "您当前吨位暂不支持专车发布";
            }
            resultDTO.setNotice(notice);
            return resultDTO;
        }

        // 匹配到运费计算规则，执行运费计算
        BigDecimal price = calculatePriceWithPriceRule(priceDTO, priceConfig);
        if (isNormalCargoOwner) {
            // 普通货主取平台运费规则，运费金额个位金额向下取整 1567 -> 1560
            price = price.movePointLeft(1).setScale(0, RoundingMode.DOWN).movePointRight(1);
        }

        resultDTO.setPrice(price);
        if (price.compareTo(new BigDecimal("0")) <= 0) {
            resultDTO.setNotice("您当前路线暂不支持专车发布");
        } else {
            resultDTO.setPriceType(priceConfig.getPriceType());
            if (Objects.equals(PriceTypeEnum.FLEXIBLE.getCode(), priceConfig.getPriceType())) {
                BigDecimal lowerLimit = Objects.isNull(priceDTO.getLowerLimit()) ? new BigDecimal("0") : priceDTO.getLowerLimit();
                resultDTO.setLowerLimit(lowerLimit);
                BigDecimal canAdjustPrice = lowerLimit.movePointLeft(2).multiply(price);
                BigDecimal lowerLimitPrice = price.subtract(canAdjustPrice).setScale(0, RoundingMode.UP);
                resultDTO.setLowerLimitPrice(lowerLimitPrice);
            }
            resultDTO.setSpecialCarTabTopAndCheck(0);
            //若货主在【专车货主管理】中，且签约合作商为非空、非平台
            if (owner != null && owner.getCooperativeId() != null
                    && owner.getCooperativeId().compareTo(0L) != 0 && owner.getCooperativeId().compareTo(1L) != 0) {
                //货主在专车货主管理中，但是签约合作商为空或者不是平台
                resultDTO.setSpecialCarTabTopAndCheck(1);
            }
            if (StringUtils.isNotBlank(priceDTO.getClientVersion()) && Integer.parseInt(priceDTO.getClientVersion()) >= 6690 && priceDTO.getUseCarType() != null && priceDTO.getUseCarType() == 1){
                //优惠价格计算
                BigDecimal perkPrice = getPerkPrice(priceDTO, price);
                if (perkPrice.compareTo(BigDecimal.ZERO) > 0){
                    resultDTO.setPerkPrice(perkPrice);
                    resultDTO.setFastDispatchWord("100%接单 不扣发货次数");
                    resultDTO.setPrice(price.subtract(perkPrice));
                }
            }
        }
        return resultDTO;
    }

    private BigDecimal getPerkPrice(CalculatePriceDTO priceDTO, BigDecimal price) {
        Integer userType = abTestRemoteService.getUserType(GOODS_PRICE_PERK_ABTEST, priceDTO.getUserId());
        if (userType == null || userType != 1) {
            return BigDecimal.ZERO;
        }

        TransportPricePerkConfigDO config = transportPricePerkConfigService.getConfigByCity(
                priceDTO.getStartCity(), priceDTO.getDestCity()
        );

        if (config == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal distance = priceDTO.getDistanceKilometer();
        BigDecimal min = new BigDecimal(config.getDistanceMin());
        BigDecimal max = new BigDecimal(config.getDistanceMax());
        BigDecimal otherFee = priceDTO.getOtherFee() == null ? BigDecimal.ZERO : priceDTO.getOtherFee();
        BigDecimal finalPrice = price.subtract(otherFee);
        BigDecimal drivingFee = priceDTO.getDrivingFee() == null ? BigDecimal.ZERO : priceDTO.getDrivingFee();
        finalPrice = finalPrice.subtract(drivingFee);
        if (distance.compareTo(min) >= 0 && distance.compareTo(max) <= 0) {
            BigDecimal ratio = new BigDecimal(config.getPerkRatio()).divide(new BigDecimal("100"));
            return finalPrice.multiply(ratio).setScale(0, RoundingMode.UP);
        }
        return BigDecimal.ZERO;
    }


    /**
     * 运费测算逻辑处理
     *
     * @param priceDTO
     * @param priceConfig
     * @return
     */
    public BigDecimal calculatePriceWithPriceRule(CalculatePriceDTO priceDTO, SpecialCarPriceConfigDO priceConfig) {
        BigDecimal result = new BigDecimal("0");

        // 使用零担运费规则
        boolean useLessPriceRule = false;
        // 配置了零担运费规则，并且货源吨位在28吨以下，使用零担运费规则计算
        String priceRule = priceConfig.getPriceRule();
        if (StringUtils.isNotEmpty(priceConfig.getLessPriceRule())) {
            String tonnage = tytConfigRemoteService.getStringValue(SPECIAL_CAR_PRICE_CONFIG_TONNAGE, "28");
            BigDecimal weight = new BigDecimal(priceDTO.getWeight());
            BigDecimal tonnageDecimal = new BigDecimal(tonnage);
            if (weight.compareTo(tonnageDecimal) <= 0) {
                priceRule = priceConfig.getLessPriceRule();
                useLessPriceRule = true;
                // 展示了用车类型则必选，如果选择的是整车，使用整车运费规则
                if (Objects.equals(UseCarTypeEnum.FULL.getCode(), priceDTO.getUseCarType())) {
                    priceRule = priceConfig.getPriceRule();
                    useLessPriceRule = false;
                }
            }
        }
        // 运费模式为灵活运价，为运价下限字段赋值
        if (Objects.equals(PriceTypeEnum.FLEXIBLE.getCode(), priceConfig.getPriceType())) {
            if (useLessPriceRule) {
                priceDTO.setLowerLimit(priceConfig.getLessPriceLowerLimit());
            } else {
                priceDTO.setLowerLimit(priceConfig.getPriceLowerLimit());
            }
        }

        List<PriceConfigBean> priceConfigBeans = convertPriceConfigBeanList(priceDTO, priceRule);
        if (CollectionUtils.isEmpty(priceConfigBeans)) {
            log.info("运费规则解析失败，priceDTO:{}, priceRule:{}, price:{}", priceDTO, priceRule, result);
            return result;
        }

        // 根据公里数计算区间运费
        result = calculateDistanceKilometerPrice(priceConfigBeans, priceDTO.getDistanceKilometer());
        log.info("运费规则解析结果，priceDTO:{}, priceRule:{}, price:{}", priceDTO, priceRule, result);

        // 根据运费配置计算出运费后才添加驾驶货物费和其他费用，否则运费为0，不允许发货
        if (result.compareTo(new BigDecimal("0")) > 0) {
            // 需要司机驾驶此类货物，添加驾驶货物费
            if (DriverDrivingEnum.DRIVING.getCode().equals(priceDTO.getDriverDriving()) && Objects.nonNull(priceConfig.getDrivingFee())) {
                result = result.add(priceConfig.getDrivingFee());
                priceDTO.setDrivingFee(priceConfig.getDrivingFee());
            }

            // 添加其他费用
            if (Objects.nonNull(priceDTO.getOtherFee())) {
                result = result.add(priceDTO.getOtherFee());
            }

            // 订金不退还，将定金添加到运费中
            // 6670 订金选择不退还时，不自动增加运费
            /*if (Objects.nonNull(priceDTO.getInfoFee())) {
                result = result.add(priceDTO.getInfoFee());
            }*/
        }

        return result.setScale(0, RoundingMode.CEILING);
    }

    /**
     * 执行公里数运费计算
     * 计算规则：
     * 1、运费配置一条为 0~200 固定价 400元 ，公里数为250公里，运费返回400
     * 2、运费配置最后一条为  100~200  固定价 400元，公里数为300公里，运费计算为：前几条运费配置计算出的价格+最后一条规则价格400
     * 3、运费配置最后一条为  100~200 公里价 10元/公里，公里数为300公里，运费计算为：前几条运费配置计算出的价格+(300-100)*10
     *
     * @param priceConfigBeans
     * @param distanceKilometer
     * @return
     */
    private BigDecimal calculateDistanceKilometerPrice(List<PriceConfigBean> priceConfigBeans, BigDecimal distanceKilometer) {
        BigDecimal result = new BigDecimal("0");
        log.info("执行公里数运费计算，priceConfigBeans:{}, distanceKilometer:{}", JSON.toJSONString(priceConfigBeans), distanceKilometer);

        //如果priceConfigBeans的最后一次区间的end小于当前距离，则直接返回0
        if (distanceKilometer.compareTo(priceConfigBeans.get(priceConfigBeans.size() - 1).getEnd()) > 0) {
            log.info("距离超出配置范围，distanceKilometer:{}", distanceKilometer);
            return result;
        }

        for (int i = 0; i < priceConfigBeans.size(); i++) {
            PriceConfigBean configBean = priceConfigBeans.get(i);
            if (distanceKilometer.compareTo(configBean.getStart()) <= 0) {
                log.info("距离在配置范围内，distanceKilometer:{}, configBean:{}", distanceKilometer, configBean);
                return result;
            } else if (distanceKilometer.compareTo(configBean.getEnd()) > 0) {
                if (PriceConfigTypeEnum.KILOMETER_PRICE.getCode().equals(configBean.getType())) {
                    // 公里价
                    BigDecimal end = configBean.getEnd();
                    if (i == priceConfigBeans.size() - 1) {
                        // 最后一个配置项，需要特殊处理，如果为公里价，需要按实际公里数计算价格
                        end = distanceKilometer;
                    }
                    BigDecimal start = configBean.getStart();
                    if (i == 0) {
                        start = new BigDecimal("0");
                    }
                    BigDecimal currentPrice = end.subtract(start).multiply(configBean.getPrice());
                    result = result.add(currentPrice);
                } else {
                    // 固定价
                    result = result.add(configBean.getPrice());
                }
            } else {
                // 公里数在当前配置区间内
                if (PriceConfigTypeEnum.KILOMETER_PRICE.getCode().equals(configBean.getType())) {
                    // 公里价
                    BigDecimal start = configBean.getStart();
                    if (i == 0) {
                        start = new BigDecimal("0");
                    }
                    BigDecimal currentPrice = distanceKilometer.subtract(start).multiply(configBean.getPrice());
                    result = result.add(currentPrice);
                } else {
                    result = result.add(configBean.getPrice());
                }
            }
        }
        return result;
    }


    /**
     * 转换价格配置json为价格配置实体列表并排序
     *
     * @param priceDTO
     * @param priceRule
     * @return
     */
    private List<PriceConfigBean> convertPriceConfigBeanList(CalculatePriceDTO priceDTO, String priceRule) {
        List<PriceConfigBean> priceConfigBeans = new ArrayList<>();
        try {
            priceConfigBeans = JSON.parseArray(priceRule, PriceConfigBean.class);
        } catch (Exception e) {
            log.error("专车运费计算价格配置转换异常，userId:{}, startCity:{}, destCity:{}, weight:{}，priceRule:{}", priceDTO.getUserId(), priceDTO.getStartCity(), priceDTO.getDestCity(), priceDTO.getWeight(), priceRule, e);
            return priceConfigBeans;
        }

        if (CollectionUtils.isNotEmpty(priceConfigBeans)) {
            try {
                priceConfigBeans.sort(Comparator.comparing(PriceConfigBean::getStart));
            } catch (Exception e) {
                log.error("专车运费价格计算运费配置排序失败，userId:{}, startCity:{}, destCity:{}, weight:{}，priceRule:{}", priceDTO.getUserId(), priceDTO.getStartCity(), priceDTO.getDestCity(), priceDTO.getWeight(), priceRule, e);
            }
        }

        return priceConfigBeans;
    }
}
