package com.teyuntong.goods.service.service.biz.transport.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 运输信息表扩展表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
@Mapper
public interface TransportMainExtendMapper extends BaseMapper<TransportMainExtendDO> {

    TransportMainExtendDO getBySrcMsgId(Long srcMsgId);

    List<TransportMainExtendDO> getBySrcMsgIds(@Param("srcMsgIds") List<Long> srcMsgIds);

    Integer getPriceTypeBySrcMsgId(@Param("srcMsgId") Long srcMsgId);

    int updateMainExtend(@Param("mainExtend")TransportMainExtendDO mainExtend);
}