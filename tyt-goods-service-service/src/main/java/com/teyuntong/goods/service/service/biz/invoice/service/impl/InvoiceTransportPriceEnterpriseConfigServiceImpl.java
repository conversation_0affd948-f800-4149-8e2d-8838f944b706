package com.teyuntong.goods.service.service.biz.invoice.service.impl;

import com.teyuntong.goods.service.service.biz.invoice.converter.InvoiceConverter;
import com.teyuntong.goods.service.service.biz.invoice.dto.InvoiceTransportConfigLogDTO;
import com.teyuntong.goods.service.service.biz.invoice.mybatis.entity.InvoiceTransportConfigLogDO;
import com.teyuntong.goods.service.service.biz.invoice.mybatis.entity.InvoiceTransportEnterpriseConfigLogDO;
import com.teyuntong.goods.service.service.biz.invoice.mybatis.entity.InvoiceTransportPriceConfigDO;
import com.teyuntong.goods.service.service.biz.invoice.mybatis.entity.InvoiceTransportPriceEnterpriseConfigDO;
import com.teyuntong.goods.service.service.biz.invoice.mybatis.mapper.InvoiceTransportConfigLogMapper;
import com.teyuntong.goods.service.service.biz.invoice.mybatis.mapper.InvoiceTransportEnterpriseConfigLogMapper;
import com.teyuntong.goods.service.service.biz.invoice.mybatis.mapper.InvoiceTransportPriceConfigMapper;
import com.teyuntong.goods.service.service.biz.invoice.mybatis.mapper.InvoiceTransportPriceEnterpriseConfigMapper;
import com.teyuntong.goods.service.service.biz.invoice.service.InvoiceTransportPriceEnterpriseConfigService;
import com.teyuntong.goods.service.service.common.enums.InvoiceServiceProviderEnum;
import com.teyuntong.goods.service.service.remote.user.ThirdEnterpriseRemoteService;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import com.teyuntong.user.service.client.enterprise.vo.InvoiceDominantVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 企业公里运价配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-11
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class InvoiceTransportPriceEnterpriseConfigServiceImpl implements InvoiceTransportPriceEnterpriseConfigService {

    private final InvoiceTransportConfigLogMapper invoiceTransportConfigLogMapper;
    private final InvoiceTransportPriceConfigMapper invoiceTransportPriceConfigMapper;
    private final InvoiceTransportEnterpriseConfigLogMapper invoiceTransportEnterpriseConfigLogMapper;
    private final InvoiceTransportPriceEnterpriseConfigMapper invoiceTransportPriceEnterpriseConfigMapper;
    private final ThirdEnterpriseRemoteService thirdEnterpriseRemoteService;

    @Override
    public InvoiceTransportConfigLogDTO getLastInvoiceTransportConfig(String serviceProviderCode) {
        List<Integer> ServiceProviderCodeTypeList = new ArrayList<>();
        ServiceProviderCodeTypeList.add(-1);
        Long userId = LoginHelper.getRequiredLoginUser().getUserId();
        WebResult<List<InvoiceDominantVo>> invoiceDominantListWebResult = thirdEnterpriseRemoteService.getInvoiceDominantList(userId);
        if (invoiceDominantListWebResult.getCode().equals(WebResult.success().getCode())) {
            List<InvoiceDominantVo> invoiceDominantList = invoiceDominantListWebResult.getData();
            if (CollectionUtils.isNotEmpty(invoiceDominantList)) {
                Set<String> ServiceProviderCode = invoiceDominantList.stream().map(InvoiceDominantVo::getServiceProviderCode).collect(Collectors.toSet());
                if (ServiceProviderCode.contains(InvoiceServiceProviderEnum.XHL.getCode())) ServiceProviderCodeTypeList.add(2);
                if (ServiceProviderCode.contains(InvoiceServiceProviderEnum.HBWJ.getCode())) ServiceProviderCodeTypeList.add(1);
            }
        }

        InvoiceTransportConfigLogDO lastConfig = invoiceTransportConfigLogMapper.getLastConfig(ServiceProviderCodeTypeList);
        if (lastConfig != null) {
            InvoiceTransportConfigLogDTO tytInvoiceTransportConfigLogDTO = InvoiceConverter.INSTANCE.convertConfigLogDO2DTO(lastConfig);
            List<InvoiceTransportPriceConfigDO> priceConfigs = invoiceTransportPriceConfigMapper.getPriceConfig();
            tytInvoiceTransportConfigLogDTO.setTytInvoiceTransportPriceConfigList(InvoiceConverter.INSTANCE.convertPriceConfigDO2DTOs(priceConfigs));

            // 如果是xhl，不进行分段支付
            if (StringUtils.isNotBlank(serviceProviderCode) && Objects.equals(serviceProviderCode, InvoiceServiceProviderEnum.XHL.getCode())) {
                tytInvoiceTransportConfigLogDTO.setSegmentedPayments(0);
            }

            return tytInvoiceTransportConfigLogDTO;
        }
        return null;
    }

    @Override
    public InvoiceTransportConfigLogDTO getLastInvoiceTransportEnterpriseConfig(Long enterpriseId, String serviceProviderCode) {
        // 该企业存在自己配置，取日志表配置；否则取公共配置
        InvoiceTransportEnterpriseConfigLogDO enterpriseConfigLogDO = invoiceTransportEnterpriseConfigLogMapper.getLastByEnterpriseId(enterpriseId);
        if (enterpriseConfigLogDO == null) {
            return getLastInvoiceTransportConfig(serviceProviderCode);
        } else {
            InvoiceTransportConfigLogDTO invoiceTransportConfigLogDTO = InvoiceConverter.INSTANCE.convertEnterpriseConfigLogDO2DTO(enterpriseConfigLogDO);
            List<InvoiceTransportPriceEnterpriseConfigDO> priceConfigList = invoiceTransportPriceEnterpriseConfigMapper.getPriceConfigByEnterpriseId(enterpriseId);
            invoiceTransportConfigLogDTO.setTytInvoiceTransportPriceConfigList(InvoiceConverter.INSTANCE.convertEnterprisePriceConfigDO2DTOs(priceConfigList));

            // 如果是xhl，不进行分段支付
            if (StringUtils.isNotBlank(serviceProviderCode) && Objects.equals(serviceProviderCode, InvoiceServiceProviderEnum.XHL.getCode())) {
                invoiceTransportConfigLogDTO.setSegmentedPayments(0);
            }

            return invoiceTransportConfigLogDTO;
        }
    }

}
