package com.teyuntong.goods.service.service.rpc.publish.builder;

import cn.hutool.core.util.ArrayUtil;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportGoodModelFactorService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportSeckillFactorService;
import com.teyuntong.goods.service.service.common.enums.PublishTypeEnum;
import com.teyuntong.goods.service.service.common.enums.SourceTypeEnum;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.bi.BiGoodModelResult;
import com.teyuntong.goods.service.service.rpc.bi.BiRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.converter.TransportPublishConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Random;

/**
 * 扩展表信息builder
 *
 * <AUTHOR>
 * @since 2025/02/21 20:01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransportExtendBuilder {

    private final BiRemoteService biRemoteService;
    private final TytConfigRemoteService tytConfigRemoteService;
    private final TransportSeckillFactorService transportSeckillFactorService;
    private final TransportGoodModelFactorService transportGoodModelFactorService;

    public void build(DirectPublishProcessBO directPublishProcessBO) {
        TransportMainExtendDO newMainExtendDO = TransportPublishConverter.INSTANCE
                .copyMainExtendDO(directPublishProcessBO.getOldMainExtend());

        if (directPublishProcessBO.getDirectPublishBO().isHistoryGoods()) {
            newMainExtendDO.setId(null);
        }

        TransportMainDO newMainDO = directPublishProcessBO.getTransportMain();
        newMainExtendDO.setCreateTime(newMainDO.getCtime());
        newMainExtendDO.setModifyTime(newMainDO.getMtime());

        // 设置BI模型分数
        BiGoodModelResult goodModel = biRemoteService.getGoodModel(newMainDO);
        if (goodModel != null) {
            newMainExtendDO.setGoodModelScore(goodModel.getScore());
            newMainExtendDO.setGoodModelLevel(goodModel.getLevel());
            newMainExtendDO.setLimGoodModelScore(goodModel.getLim_score());
            newMainExtendDO.setLimGoodModelLevel(goodModel.getLim_level());
            directPublishProcessBO.setBiGoodModelResult(goodModel);
        }

        // 设置BI抽佣分数
        if (StringUtils.isNotBlank(newMainDO.getPrice())) {
            BiGoodModelResult goodsModelPrice = biRemoteService.getGoodsModelPrice(newMainDO);
            if (goodsModelPrice != null) {
                newMainExtendDO.setCommissionScore(goodsModelPrice.getScore());
            }
        }
        // 设置秒抢货源，后面保存extend时再设置 seckillGoods字段，因为需要根据srcMsgId字段判断实验组、对照组
        boolean isSeckillGoods = this.checkIsSeckillGoods(newMainDO, newMainExtendDO.getCommissionScore());
        directPublishProcessBO.setSeckillGoods(isSeckillGoods);

        // 设置好中差货标签
        newMainExtendDO.setGoodTransportLabel(transportGoodModelFactorService.judgeModelLevel(newMainDO));

        directPublishProcessBO.setMainExtend(newMainExtendDO);
    }

    /**
     * 判断是否是秒抢货源
     * 1. 剔除：开票/专车/多车的货源/YMM的货源/后台发的货
     * 2. 一口价
     * 2. 定价不能为0
     * 4. 运价分=0.00的货源 或运价分超过10分 或 目的地命中圈定城市
     *
     * @since 6600
     */
    public boolean checkIsSeckillGoods(TransportMainDO transport, BigDecimal commissionScore) {
        // 是否开启秒抢货源判断开关
        Integer switchV = tytConfigRemoteService.getIntValue("turn_on_seckill_goods_switch", 0);
        if (switchV == 0) {
            return false;
        }
        // 非一口价直接返回
        if (!Objects.equals(transport.getPublishType(), PublishTypeEnum.FIXED.getCode())) {
            log.info("checkIsSeckillGoods 非一口价，srcMsgId:{}", transport.getSrcMsgId());
            return false;
        }
        // 定金不能为0
        if (transport.getInfoFee() == null || transport.getInfoFee().compareTo(BigDecimal.ZERO) <= 0) {
            log.info("checkIsSeckillGoods 定金为0，srcMsgId:{}", transport.getSrcMsgId());
            return false;
        }

        // 非 开票/专车/多车的货源/YMM的货源/后台发的货 直接返回
        if (Objects.equals(transport.getInvoiceTransport(), 1)
                || Objects.equals(transport.getExcellentGoods(), 2)
                || (transport.getShuntingQuantity() != null && transport.getShuntingQuantity() > 1)
                || !ArrayUtil.contains(new int[]{SourceTypeEnum.NORMAL.getCode(), SourceTypeEnum.OWNER.getCode()}, transport.getSourceType())) {
            log.info("checkIsSeckillGoods 非APP发货，srcMsgId:{}", transport.getSrcMsgId());
            return false;
        }
        // 去掉长宽高重限制
        /*if (StringUtils.isBlank(transport.getWeight()) || Double.parseDouble(transport.getWeight()) <= 0
                || StringUtils.isBlank(transport.getLength()) || Double.parseDouble(transport.getLength()) <= 0
                || StringUtils.isBlank(transport.getWide()) || Double.parseDouble(transport.getWide()) <= 0
                || StringUtils.isBlank(transport.getHigh()) || Double.parseDouble(transport.getHigh()) <= 0) {
            log.info("checkIsSeckillGoods 长宽高重为0，srcMsgId:{}", transport.getSrcMsgId());
            return false;
        }*/
        // 运价分=0.00的货源 或运价分超过10分 或 目的地命中圈定城市 ，打标
        Integer score = tytConfigRemoteService.getIntValue("turn_on_seckill_goods_score", 10);
        log.info("checkIsSeckillGoods 运价分超过10分或目的地命中圈定城市，srcMsgId:{}，seckillGoodsScore：{}，commissionScore:{}", transport.getSrcMsgId(), score, commissionScore);
        return commissionScore == null || commissionScore.compareTo(BigDecimal.ZERO) == 0
                || commissionScore.compareTo(new BigDecimal(score)) > 0
                || transportSeckillFactorService.existDestCity(transport.getDestCity()) > 0;
    }
}
