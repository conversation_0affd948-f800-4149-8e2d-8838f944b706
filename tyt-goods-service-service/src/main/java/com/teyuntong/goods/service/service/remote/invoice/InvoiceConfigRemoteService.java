package com.teyuntong.goods.service.service.remote.invoice;

import com.teyuntong.goods.service.client.invoice.service.InvoiceConfigRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * 开票配置远程服务
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "invoiceConfigRemoteService",
        fallbackFactory = InvoiceConfigRemoteService.InvoiceConfigRemoteServiceFallbackFactory.class)
public interface InvoiceConfigRemoteService extends InvoiceConfigRpcService {

    @Component
    class InvoiceConfigRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<InvoiceConfigRemoteService> {
        protected InvoiceConfigRemoteServiceFallbackFactory() {
            super(true, InvoiceConfigRemoteService.class);
        }
    }
}
