package com.teyuntong.goods.service.service.biz.transport.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.service.client.transport.dto.TransportCountDTO;
import com.teyuntong.goods.service.client.transport.dto.TransportListDTO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 运输信息表主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
@Mapper
public interface TransportMainMapper extends BaseMapper<TransportMainDO> {

    /**
     * 车签署协议更新货源相关字段数据
     *
     * @param transportMainDO 参数
     */
    void updateCarAgreementAboutTransportMainSomeFieldBySrcMsgId(TransportMainDO transportMainDO);

    /**
     * 根据货源ID查询货源是否存在，如果存在，返回值中将有ID信息
     *
     * @param srcMsgId 货源ID
     */
    TransportMainDO isHaveTransportMainBySrcMsgIdAndStatus(@Param("srcMsgId") Long srcMsgId);

    BigDecimal selectEnterpriseTaxRateByUserIdNoStatus(@Param("useId") Long useId);

    /**
     * 获取货主发布中的开票货源ID集合
     *
     * @param userId 货主ID
     * @return 货源ID列表
     */
    List<Long> getInReleaseInvoiceTransportSrcMsgIdList(@Param("userId") Long userId, @Param("todayStartDate") Date todayStartDate);

    List<Long> getInReleaseAndNoPriceTransportSrcMsgIdList(@Param("userId") Long userId, @Param("todayStartDate") Date todayStartDate);

    /**
     * 获取货主发布中的货源ID集合
     *
     * @param userId
     * @return
     */
    List<Long> getInReleaseTransportIdList(@Param("userId") Long userId);

    List<Long> getCallCountParamBySrcMsgIdList(@Param("srcMsgIdList") List<Long> srcMsgIdList, @Param("callCount") Integer callCount);

    List<Long> getViewCountParamBySrcMsgIdList(@Param("srcMsgIdList") List<Long> srcMsgIdList, @Param("viewCount") Integer viewCount);

    List<Long> getInReleaseTransportAndCtimeEarlierSrcMsgIdList(@Param("srcMsgIdList") List<Long> srcMsgIdList, @Param("todayStartDate") Date todayStartDate, @Param("earlierDate") Date earlierDate);

    List<Long> getDynamicCarUserDataList(@Param("srcMsgIdList") List<Long> srcMsgIdList);

    List<TransportMainDO> getTodayPublishTransport(@Param("userId") Long userId);

    /**
     * 货源列表-已取消
     *
     * @param dto
     * @param pageSize
     * @return
     */
    List<TransportMainDO> getCanceledList(@Param("req") TransportListDTO req,
                                          @Param("pageSize") Integer pageSize,
                                          @Param("startTime") Date startTime,
                                          @Param("endTime") Date endTime);

    /**
     * 货源列表-已过期
     *
     * @param dto
     * @param pageSize
     * @param startTime
     * @param endTime
     * @return
     */
    List<TransportMainDO> getExpiredList(@Param("req") TransportListDTO req,
                                         @Param("pageSize") Integer pageSize,
                                         @Param("startTime") Date startTime,
                                         @Param("endTime") Date endTime);

    void saveTransportDynamicShowLog(@Param("srcMsgIdList") List<String> srcMsgIdList);

    TransportMainDO getFirstTransport(@Param("transportUserId") Long transportUserId);

    Integer IsHaveGoodCarPriceTransportBySrcMsgIdList(@Param("srcMsgIds") List<Long> srcMsgIds);

    TransportMainDO getLastTransportByTransportUserId(@Param("userId") Long userId);

    void initRecordGoodsTransactionInfo(@Param("srcMsgId") Long srcMsgId, @Param("userId") Long userId, @Param("taskContent") String taskContent, @Param("goodTypeName") String goodTypeName
            , @Param("distance") String distance, @Param("startCity") String startCity, @Param("destCity") String destCity
            , @Param("weight") String weight, @Param("price") String price);

    // 获取上一条相同货源
    TransportMainDO getLastSameDealTransport(TransportMainDO transportMainDO);


    List<String> selectOfPublishType(@Param("userId") Long userId, @Param("publishTime") Date publishTime);

    Date hasUserTransportLast30Day(@Param("userId") Long userId);

    List<TransportMainDO> getUserSomeDayTransportData(@Param("userId") Long userId,
                                                      @Param("someDay") String someDay);

    Integer getPublishCountByUserId(@Param("userId") Long userId,
                                    @Param("startTime") Date startTime,
                                    @Param("endTime") Date endTime);

    List<TransportMainDO> getUserCommissionGoods(@Param("userId") Long userId);

    Integer getSameCityTransportAllCount(@Param("startCity") String startCity, @Param("destCity") String destCity, @Param("type") int type
            , @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    Integer getSameCityTransportBargainCont(@Param("startCity") String startCity, @Param("destCity") String destCity, @Param("type") int type
            , @Param("startTime") Date startTime, @Param("endTime") Date endTime);


    Integer selectDistanceBySrcMsgId(@Param("srcMsgId") Long srcMsgId);


    void saveTransportCreditExposure(@Param("srcMsgId") Long srcMsgId, @Param("creditRetop") Integer creditRetop);

    /**
     * 校验是否有重货
     */
    int countDuplicateTransport(@Param("hashCode") String hashCode, @Param("startTime") String startTime,
                                @Param("userId") Long userId, @Param("telsStr") String telsStr);

    /**
     * 返回相似货源第一条货源
     */
    TransportMainDO getFirstSimilarityGoods(String similarityCode);

    /**
     * 将货源设置为不显示状态
     *
     * @param srcMsgId
     */
    void noDisplay(@Param("srcMsgId") Long srcMsgId);

    void invalidTransport(@Param("srcMsgId") Long srcMsgId);

    /**
     * 用户当天发布中货源数量
     *
     * @param userId
     * @return
     */
    int getUserDayPublishCount(@Param("userId") Long userId);


    List<Long> getInReleaseTransport(@Param("srcMsgIdList") List<Long> srcMsgIdList);

    /**
     * 用户货源数量统计
     *
     * @param transportCountDTO
     * @return
     */
    int getTransportCountForUserId(@Param("transportCountDTO") TransportCountDTO transportCountDTO);

    /**
     * 修改display_type，使货源隐藏
     *
     * @param srcMsgId
     */
    void updateDisplayTypeToHide(Long srcMsgId);

    int countSimilarityGoods(@Param("srcMsgId") Long srcMsgId, @Param("similarityCode") String similarityCode);

    int countSameRouteGoods(@Param("srcMsgId") Long srcMsgId,
                            @Param("startCity") String startCity,
                            @Param("destCity") String destCity);

    List<Long> getHistoryIdListBySrcMsgId(@Param("srcMsgId") Long srcMsgId, @Param("userId") Long userId, @Param("todayStartDate") Date todayStartDate);


    TransportMainDO getLastTransport(@Param("userId") Long userId);

    int updateDisplayTypeByIds(@Param("idList") List<Long> idList, @Param("display") Integer display);

    void updateLabelJsonBySrcMsgId(@Param("srcMsgId") Long srcMsgId, @Param("labelJson") String labelJson);

    void updateStatusById(@Param("status") Integer status, @Param("display") Integer display, @Param("infoStatus") String infoStatus, @Param("srcMsgId") Long srcMsgId);

    List<Long> getTodayIdListBySrcMsgId(@Param("srcMsgId") Long srcMsgId, @Param("userId") Long userId, @Param("status") Integer status);

    /**
     * 更新货源信息
     *
     * @param transportMain
     * @return
     */
    int updateTransportMain(@Param("transportMain") TransportMainDO transportMain);

    List<TransportMainDO> getSimilarityForOrderCancelPush(@Param("transportMain") TransportMainDO transportMain, @Param("excludeSrcMsgIds") List<Long> excludeSrcMsgIds);

    List<Long> getTransportForUser(@Param("userId") Long userId, @Param("status") Integer status, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
