package com.teyuntong.goods.service.service.biz.publish.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.service.service.biz.publish.mybatis.entity.TransportPricePerkConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 运费补贴配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-05-29
 */
@Mapper
public interface TransportPricePerkConfigMapper extends BaseMapper<TransportPricePerkConfigDO> {

    TransportPricePerkConfigDO getConfigByCity(@Param("startCity") String startCity, @Param("destCity") String destCity);
}
