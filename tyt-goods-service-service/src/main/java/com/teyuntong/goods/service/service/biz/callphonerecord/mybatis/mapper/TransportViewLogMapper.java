package com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.TransportViewLogDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * <p>
 * 货物详情浏览日志表，每人每货仅存储一条 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Mapper
public interface TransportViewLogMapper extends BaseMapper<TransportViewLogDO> {

    /**
     * 获取N次查看之后的最早拨打时间
     */
    Date getLastViewTimeOffset(@Param("srcMsgId") Long srcMsgId, @Param("offset") Integer offset);

    int isViewed(@Param("userId") Long userId, @Param("srcMsgId") Long srcMsgId);

    void insertIgnore(TransportViewLogDO viewLogDO);
}
