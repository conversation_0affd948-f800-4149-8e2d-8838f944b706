package com.teyuntong.goods.service.service.biz.publish.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 运费补贴配置表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-05-29
 */
@Getter
@Setter
@TableName("tyt_transport_price_perk_config")
public class TransportPricePerkConfigDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 路线 出发地城市
     */
    private String startCity;

    /**
     * 路线 目的地城市
     */
    private String destCity;

    /**
     * 生效开始时间
     */
    private Date startTime;

    /**
     * 生效结束时间
     */
    private Date endTime;

    /**
     * 补贴比例
     */
    private Integer perkRatio;

    /**
     * 运距范围 最小值
     */
    private Integer distanceMin;

    /**
     * 运距范围 最大值
     */
    private Integer distanceMax;

    /**
     * 0禁用 1启用
     */
    private Integer status;

    /**
     * 删除状态 0否 1是
     */
    private Integer deleteStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 操作人
     */
    private String operator;
}
