package com.teyuntong.goods.service.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 货源模型等级枚举
 */
@Getter
@AllArgsConstructor
public enum TransportModelLevelEnum {
    GOOD_1(11, "好1"),
    GOOD_2(12, "好2"),
    GOOD_3(13, "好3"),
    MID_1(21, "中1"),
    MID_2(22, "中2"),
    MID_3(23, "中3"),
    LOW_1(31, "差1"),
    LOW_2(32, "差2"),
    ;

    private final Integer code;
    private final String name;

}