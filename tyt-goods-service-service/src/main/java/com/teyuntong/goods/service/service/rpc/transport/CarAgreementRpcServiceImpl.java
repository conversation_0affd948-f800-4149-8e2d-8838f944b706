package com.teyuntong.goods.service.service.rpc.transport;

import com.alibaba.fastjson.JSON;
import com.teyuntong.goods.service.client.transport.dto.TransportMainDTO;
import com.teyuntong.goods.service.client.transport.service.CarAgreementRpcService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportMainMapper;
import com.teyuntong.goods.service.service.biz.transport.service.TransportMainService;
import com.teyuntong.goods.service.service.biz.transport.service.TransportService;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.rpc.publish.checker.InvoiceTransportChecker;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Objects;

/**
 * 运输代调服务 业务逻辑
 *
 * <AUTHOR>
 * @since 2024/01/17 16:45
 */
@RestController
@RequiredArgsConstructor
@Slf4j
public class CarAgreementRpcServiceImpl implements CarAgreementRpcService {

    private final TransportMainService transportMainService;

    private final TransportService transportService;

    private final TransportMainMapper transportMainMapper;

    private static final BigDecimal ONE_HUNDRED_BIGDECIMAL = new BigDecimal(100);
    private final InvoiceTransportChecker invoiceTransportChecker;

    @Override
    @Transactional
    public void carAgreementUpdateTransport(TransportMainDTO transportMainDTO) {
        log.info("协议修改货源信息 {}", JSON.toJSONString(transportMainDTO));
        if (transportMainDTO == null || transportMainDTO.getSrcMsgId() == null) {
            throw new BusinessException(GoodsErrorCode.ERROR_NO_PARAM);
        }


        TransportMainDO transportMainResult = transportMainService.isHaveTransportMainBySrcMsgIdAndStatus(transportMainDTO.getSrcMsgId());
        if (transportMainResult == null) {
            throw new BusinessException(GoodsErrorCode.ERROR_NO_TRANSPORT);
        }

        if (StringUtils.isNotBlank(transportMainDTO.getPrice())) {
            transportMainDTO.setPrice(new BigDecimal(transportMainDTO.getPrice()).setScale(0, BigDecimal.ROUND_DOWN).toString());
        }
        if (StringUtils.isNotBlank(transportMainDTO.getAdditionalPrice())) {
            transportMainDTO.setAdditionalPrice(new BigDecimal(transportMainDTO.getAdditionalPrice()).setScale(0, BigDecimal.ROUND_DOWN).toString());
        }

        if (transportMainDTO.getPaymentsType() != null && Objects.equals(transportMainDTO.getPaymentsType(), 1)) {
            BigDecimal newPrice = StringUtils.isBlank(transportMainDTO.getPrice()) ? BigDecimal.ZERO : new BigDecimal(transportMainDTO.getPrice());
            BigDecimal prepaidPrice = transportMainDTO.getPrepaidPrice();
            BigDecimal collectedPrice = transportMainDTO.getCollectedPrice();
            BigDecimal receiptPrice = transportMainDTO.getReceiptPrice();

            if (prepaidPrice != null) {
                prepaidPrice = prepaidPrice.setScale(0, BigDecimal.ROUND_DOWN);
                transportMainDTO.setPrepaidPrice(prepaidPrice);
                newPrice = newPrice.add(prepaidPrice);
            }
            if (collectedPrice != null) {
                collectedPrice = collectedPrice.setScale(0, BigDecimal.ROUND_DOWN);
                transportMainDTO.setCollectedPrice(collectedPrice);
                newPrice = newPrice.add(collectedPrice);
            }
            if (receiptPrice != null) {
                receiptPrice = receiptPrice.setScale(0, BigDecimal.ROUND_DOWN);
                transportMainDTO.setReceiptPrice(receiptPrice);
                newPrice = newPrice.add(receiptPrice);
            }
            if(newPrice.compareTo(BigDecimal.ZERO)>0) {
                transportMainDTO.setPrice(newPrice.toString());
            }
            invoiceTransportChecker.checkSegmentedPayments(prepaidPrice,collectedPrice,receiptPrice,transportMainDTO.getPrice(),transportMainResult.getUserId(),false,false, null);

        }
        TransportMainDO transportMainDO = new TransportMainDO();
        TransportDO transportDO = new TransportDO();
        BeanUtils.copyProperties(transportMainDTO, transportMainDO);
        BeanUtils.copyProperties(transportMainDO, transportDO);
        transportMainService.updateCarAgreementAboutTransportMainSomeFieldBySrcMsgId(transportMainDO);
        transportService.updateCarAgreementAboutTransportSomeFieldBySrcMsgId(transportDO);
    }

    public HashMap<String, String> getAdditionalPriceAndEnterpriseTaxRate(Long srcMsgId, String newPrice) {
        if (srcMsgId == null || StringUtils.isBlank(newPrice)) {
            return null;
        }

        BigDecimal priceInt = new BigDecimal(newPrice);

        TransportMainDO transportMainDO = transportMainService.getById(srcMsgId);
        if (transportMainDO == null || transportMainDO.getUserId() == null) {
            return null;
        }

        BigDecimal enterpriseTaxRate = transportMainMapper.selectEnterpriseTaxRateByUserIdNoStatus(transportMainDO.getUserId());
        if (enterpriseTaxRate == null) {
            return null;
        }

        HashMap<String, String> result = new HashMap<>();

        BigDecimal additionalPrice = calculateAdditionalFreight(enterpriseTaxRate, priceInt);


        result.put("additionalPrice", additionalPrice.toString());
        result.put("enterpriseTaxRate", enterpriseTaxRate.toString());

        return result;
    }

    public BigDecimal calculateAdditionalFreight(BigDecimal enterpriseTaxRate, BigDecimal price) {
        enterpriseTaxRate = enterpriseTaxRate.divide(ONE_HUNDRED_BIGDECIMAL);
        if (enterpriseTaxRate.compareTo(BigDecimal.ZERO) <= 0 || enterpriseTaxRate.compareTo(BigDecimal.ONE) >= 0) {
            throw new IllegalArgumentException("企业税率应在0和1之间");
        }
        BigDecimal oneMinusTaxRate = BigDecimal.ONE.subtract(enterpriseTaxRate);
        return price.divide(oneMinusTaxRate, 2, BigDecimal.ROUND_UP).subtract(price);
    }

}
