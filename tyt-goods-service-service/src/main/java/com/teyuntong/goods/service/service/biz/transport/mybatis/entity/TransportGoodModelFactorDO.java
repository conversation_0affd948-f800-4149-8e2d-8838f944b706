package com.teyuntong.goods.service.service.biz.transport.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 好中差货模型因子
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Getter
@Setter
@TableName("tyt_transport_good_model_factor")
public class TransportGoodModelFactorDO {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 模型等级： 11:好1，12:好2，13:好3，21:中1，22:中2，23:中3，31:差1，32:差2
     */
    private Integer modelLevel;

    /**
     * 吨重区间（左闭右开）起始值
     */
    private Integer weightStart;

    /**
     * 吨重区间（左闭右开）终止值
     */
    private Integer weightEnd;

    /**
     * 距离（公里）起始值
     */
    private Integer distanceStart;

    /**
     * 距离（公里）起始值
     */
    private Integer distanceEnd;

    /**
     * 固定价下限
     */
    private BigDecimal priceLower;

    /**
     * 固定价上限
     */
    private BigDecimal priceUpper;

    /**
     * 吨公里价下限
     */
    private BigDecimal unitPriceLower;

    /**
     * 吨公里价上限
     */
    private BigDecimal unitPriceUpper;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createTime;
}
