package com.teyuntong.goods.service.service.rpc.transport;

import com.teyuntong.goods.service.client.transport.dto.TransportDispatchDTO;
import com.teyuntong.goods.service.client.transport.service.TransportDispatchRpcService;
import com.teyuntong.goods.service.client.transport.vo.TransportDispatchVO;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.TransportDispatchViewService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportDispatchDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportDispatchService;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.common.utils.TytBeanUtil;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 运输代调服务 业务逻辑
 *
 * <AUTHOR>
 * @since 2024/01/17 16:45
 */
@RestController
@RequiredArgsConstructor
public class TransportDispatchRpcServiceImpl implements TransportDispatchRpcService {

    private final TransportDispatchService transportDispatchService;
    private final TransportDispatchViewService transportDispatchViewService;

    @Override
    public TransportDispatchVO query(TransportDispatchDTO transportDispatchDTO) {
        TransportDispatchDO transportDispatchDO = transportDispatchService.selectOne(transportDispatchDTO);
        return TytBeanUtil.convertBean(transportDispatchDO, TransportDispatchVO.class);
    }

    @Override
    public TransportDispatchVO queryBySrcMsgId(Long srcMsgId) {
        if (srcMsgId == null || srcMsgId == 0L) {
            throw new BusinessException(GoodsErrorCode.ERROR_SRC_MSG_ID_LACK);
        }
        TransportDispatchDTO dto = new TransportDispatchDTO();
        dto.setSrcMsgId(srcMsgId);
        TransportDispatchDO transportDispatchDO = transportDispatchService.selectOne(dto);
        return TytBeanUtil.convertBean(transportDispatchDO, TransportDispatchVO.class);
    }

    @Override
    public Integer hasContactInSrcMsgIds(List<Long> srcMsgIds) {
        return transportDispatchViewService.hasContactInSrcMsgIds(srcMsgIds);
    }
}
