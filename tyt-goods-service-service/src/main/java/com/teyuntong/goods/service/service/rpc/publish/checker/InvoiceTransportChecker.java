package com.teyuntong.goods.service.service.rpc.publish.checker;

import com.teyuntong.goods.service.client.publish.dto.UpdateGoodsInfoDTO;
import com.teyuntong.goods.service.service.biz.invoice.dto.InvoiceTransportConfigLogDTO;
import com.teyuntong.goods.service.service.biz.invoice.dto.InvoiceTransportPriceConfigDTO;
import com.teyuntong.goods.service.service.biz.invoice.dto.InvoiceTransportTaxInfoDTO;
import com.teyuntong.goods.service.service.biz.invoice.mybatis.entity.InvoiceEnterpriseDO;
import com.teyuntong.goods.service.service.biz.invoice.mybatis.entity.OnlineFreightEnterpriseDO;
import com.teyuntong.goods.service.service.biz.invoice.service.InvoiceEnterpriseService;
import com.teyuntong.goods.service.service.biz.invoice.service.InvoiceTransportPriceEnterpriseConfigService;
import com.teyuntong.goods.service.service.biz.invoice.service.OnlineFreightEnterpriseService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportEnterpriseLogDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportEnterpriseLogService;
import com.teyuntong.goods.service.service.common.enums.ExcellentGoodsEnums;
import com.teyuntong.goods.service.service.common.enums.InvoiceServiceProviderEnum;
import com.teyuntong.goods.service.service.common.enums.YesOrNoEnum;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.service.service.remote.basic.PublicResourceRemoteService;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.user.ThirdEnterpriseRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.infra.basic.resource.client.pubresource.vo.PublicResourceVO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static cn.hutool.core.text.StrPool.COMMA;
import static com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant.INVOICE_SUBJECT_DATA;
import static com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant.SEGMENTED_PAYMENTS_PRICE;

/**
 * 开票货源校验
 *
 * <AUTHOR>
 * @since 2025/02/21 13:12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InvoiceTransportChecker {

    private final ThirdEnterpriseRemoteService thirdEnterpriseRemoteService;
    private final InvoiceEnterpriseService invoiceEnterpriseService;
    private final OnlineFreightEnterpriseService onlineFreightEnterpriseService;
    private final ABTestRemoteService abTestRemoteService;
    private final InvoiceTransportPriceEnterpriseConfigService invoiceTransportPriceEnterpriseConfigService;
    private final TransportEnterpriseLogService transportEnterpriseLogService;
    private final PublicResourceRemoteService publicResourceRemoteService;


    private static final BigDecimal DEFAULT_ENTERPRISE_TAX_RATE = new BigDecimal("6.6");
    private static final BigDecimal HUNDRED = new BigDecimal("100");

    private final TytConfigRemoteService tytConfigRemoteService;

    /**
     * 校验开票货源
     *
     * @param publishBO
     * @param user
     */
    public void checkInvoiceTransport(PublishBO publishBO, UserRpcVO user) {
        if (Objects.equals(publishBO.getInvoiceTransport(), YesOrNoEnum.YES.getId())) {
            // 开票货源必须要有距离
            if (publishBO.getDistance() == null) {
                throw BusinessException.createException(GoodsErrorCode.DISTANCE_IS_NULL);
            }

            String subjectData = tytConfigRemoteService.getStringValue(INVOICE_SUBJECT_DATA, "1,JCZY");
            String[] split = subjectData.split(COMMA);
            String ownSubjectId = split[0];
            String ownSubjectCode = split[1];
            if (publishBO.getInvoiceSubjectId() == null || StringUtils.isBlank(publishBO.getServiceProviderCode())) {
                publishBO.setInvoiceSubjectId(Long.parseLong(ownSubjectId));
                publishBO.setServiceProviderCode(ownSubjectCode);
            }
            if (Objects.equals(publishBO.getExcellentGoods(), ExcellentGoodsEnums.SPECIAL.getCode()) && Objects.equals(publishBO.getServiceProviderCode(), ownSubjectCode)) {
                // 专车不允许选自有平台
                throw BusinessException.createException(GoodsErrorCode.SPECIAL_NO_PUBLISH_OWN_SUBJECT);
            }
            // 校验开票货源货主资格
            checkPublishInvoiceTransportQualification(user.getId(), publishBO.getInvoiceSubjectId(), false);
            // 校验开票货源发布参数
            checkPublishInvoiceTransportParam(user.getId(), publishBO.getDistance().toString(), publishBO.getPrice(), publishBO.getWeight());
            // 翔和翎开票货源校验
            checkXhlInvoiceTransport(publishBO, user);

            // 如果是分段支付，校验分段运费
            if (Objects.equals(publishBO.getPaymentsType(), 1)) {
                checkSegmentedPayments(publishBO.getPrepaidPrice(), publishBO.getCollectedPrice(),
                        publishBO.getReceiptPrice(), publishBO.getPrice(), user.getId(), false,true, publishBO.getServiceProviderCode());
            }

        }
    }

    /**
     * @param prepaidPrice
     * @param collectedPrice
     * @param receiptPrice
     * @param price
     * @param userId
     * @param direct         是否走直接发布
     * @param checkPaymentsType         是否需要校验支持分段配置
     */
    public void checkSegmentedPayments(BigDecimal prepaidPrice,
                                       BigDecimal collectedPrice,
                                       BigDecimal receiptPrice,
                                       String price,
                                       Long userId, boolean direct,boolean checkPaymentsType, String serviceProviderCode) {
        // 如果到付金额不为空，就是分段支付
        if (collectedPrice == null || Objects.equals(collectedPrice, BigDecimal.ZERO)) {
            throw BusinessException.createException(GoodsErrorCode.SEGMENTED_PAYMENTS_PROPORTION_ERROR.getCode(), "分段支付到付运费必填");

        }
        prepaidPrice = prepaidPrice == null ? BigDecimal.ZERO : prepaidPrice;
        receiptPrice = receiptPrice == null ? BigDecimal.ZERO : receiptPrice;
        // 先判断当前是否允许分段支付
        if(checkPaymentsType){
            InvoiceEnterpriseDO invoiceEnterpriseDO = invoiceEnterpriseService.getByCertigierUserId(userId);
            if (invoiceEnterpriseDO != null) {
                InvoiceTransportConfigLogDTO config = invoiceTransportPriceEnterpriseConfigService.getLastInvoiceTransportEnterpriseConfig(invoiceEnterpriseDO.getId(), serviceProviderCode);
                if (config == null || config.getSegmentedPayments() != 1) {
                    if (direct) {
                        throw BusinessException.createException(GoodsErrorCode.SEGMENTED_PAYMENTS_PROPORTION_ERROR.getCode(), "当前不支持分段支付，请使用编辑再发布");
                    } else {
                        throw BusinessException.createException(GoodsErrorCode.SEGMENTED_PAYMENTS_PROPORTION_ERROR.getCode(), "当前不支持分段支付，请选择全额到付");
                    }
                }
            }
        }
        BigDecimal totalPrice = collectedPrice.add(prepaidPrice).add(receiptPrice);
        if (StringUtils.isBlank(price) || new BigDecimal(price).compareTo(totalPrice) != 0) {
            throw BusinessException.createException(GoodsErrorCode.SEGMENTED_PAYMENTS_PROPORTION_ERROR.getCode(), "分段支付运费与运费总价不一致，请修改");
        }
        BigDecimal prepaidProportion = BigDecimal.valueOf(0.3);
        BigDecimal receiptProportion = BigDecimal.valueOf(0.2);
        PublicResourceVO publicResourceVO = publicResourceRemoteService.getByName(SEGMENTED_PAYMENTS_PRICE);
        String proportion = "30%,20%";
        if (publicResourceVO != null) {
            proportion = publicResourceVO.getValue();
            if (StringUtils.isNotBlank(proportion)) {
                prepaidProportion = new BigDecimal(proportion.split(COMMA)[0].replace("%", "")).divide(HUNDRED, 2, RoundingMode.HALF_UP);
                receiptProportion = new BigDecimal(proportion.split(COMMA)[1].replace("%", "")).divide(HUNDRED, 2, RoundingMode.HALF_UP);
            }
        }
        BigDecimal priceNumber = new BigDecimal(price);
        if (!prepaidPrice.equals(BigDecimal.ZERO)) {
            // 计算预付运费上限
            BigDecimal maxPrepaidPrice = priceNumber.multiply(prepaidProportion).setScale(2, RoundingMode.HALF_UP);
            if (maxPrepaidPrice.compareTo(prepaidPrice) < 0) {
                throw BusinessException.createException(GoodsErrorCode.SEGMENTED_PAYMENTS_PROPORTION_ERROR.getCode(), "预付运费需低于司机运费" + proportion.split(COMMA)[0] + ", 请修改");
            }
        }
        if (!receiptPrice.equals(BigDecimal.ZERO)) {
            // 计算回单付运费上限
            BigDecimal maxReceipt = priceNumber.multiply(receiptProportion).setScale(2, RoundingMode.HALF_UP);
            if (maxReceipt.compareTo(receiptPrice) < 0) {
                throw BusinessException.createException(GoodsErrorCode.SEGMENTED_PAYMENTS_PROPORTION_ERROR.getCode(), "回单付运费需低于司机运费" + proportion.split(COMMA)[1] + ", 请修改");
            }
        }

    }


    /**
     * 直接发布
     */
    public void checkDirectPublish(DirectPublishProcessBO processBO) {
        DirectPublishBO directPublishBO = processBO.getDirectPublishBO();
        TransportMainDO oldMain = processBO.getOldMain();

        if (Objects.equals(oldMain.getInvoiceTransport(), 1)) {
            TransportEnterpriseLogDO enterpriseLogDO = transportEnterpriseLogService.getBySrcMsgId(oldMain.getSrcMsgId());

            // 指派了车方的开票货源不允许直接发布
            if (PublishOptEnum.DIRECT.equals(processBO.getOptEnum())) {
                if (enterpriseLogDO != null && StringUtils.isNotBlank(enterpriseLogDO.getAssignCarTel())) {
                    throw new BusinessException(GoodsErrorCode.ASSIGN_TRANSPORT_PUBLISH_ERROR);
                }
            }

            Long invoiceSubjectId = Optional.ofNullable(enterpriseLogDO).map(TransportEnterpriseLogDO::getInvoiceSubjectId).orElse(null);
            // 开票货源货主资格校验
            this.checkPublishInvoiceTransportQualification(oldMain.getUserId(), invoiceSubjectId, true);

            // 开票货源限制校验
            // 如果目的地修改，距离取最新距离
            String distance = Optional.ofNullable(directPublishBO.getUpdateGoodsInfoDTO()).map(UpdateGoodsInfoDTO::getDistance).orElse(BigDecimal.ZERO).toString();
            String price = Optional.ofNullable(directPublishBO.getPrice()).orElse(oldMain.getPrice());
            this.checkPublishInvoiceTransportParam(oldMain.getUserId(), distance, price, oldMain.getWeight());

            // 校验开票货源分段支付
            if (enterpriseLogDO != null && Objects.equals(enterpriseLogDO.getPaymentsType(), YesOrNoEnum.YES.getId())) {
                checkSegmentedPayments(enterpriseLogDO.getPrepaidPrice(), enterpriseLogDO.getCollectedPrice(),
                        enterpriseLogDO.getReceiptPrice(), price, oldMain.getUserId(), true,true, enterpriseLogDO.getServiceProviderCode());
            }

            // 直接发布重新计算附加运费
            log.info("重新计算附加运费 {}, {}, {}, {}", oldMain.getSrcMsgId(), price, oldMain.getUserId(), invoiceSubjectId);
            InvoiceTransportTaxInfoDTO invoiceTransportTaxInfoDTO = this.getAdditionalAndTaxRate(oldMain.getUserId(), price, invoiceSubjectId);

            processBO.getDirectPublishBO().setEnterpriseTaxRate(invoiceTransportTaxInfoDTO.getEnterpriseTaxRate());
            processBO.getDirectPublishBO().setAdditionalPrice(Optional.ofNullable(invoiceTransportTaxInfoDTO.getAdditionalPrice()).map(BigDecimal::toString).orElse("0"));
        }
    }


    /**
     * 校验开票货源货主资格
     */
    public void checkPublishInvoiceTransportQualification(Long userId, Long invoiceSubjectId, boolean isDirectPublish) {
        if (userId == null || invoiceSubjectId == null) {
            throw new BusinessException(CommonErrorCode.ERROR_PARAMETER_LACK, null);
        }

        InvoiceEnterpriseDO invoiceEnterpriseDO = invoiceEnterpriseService.getByCertigierUserId(userId);
        if (invoiceEnterpriseDO == null) {
            throw BusinessException.createException(GoodsErrorCode.FAIL_ENTERPRISE_CERTIFICATION.getCode(), "未通过企业认证不可发布“专票”货源，如有需要，可联系客服获取帮助");
        }

        // 直接发布不校验ab测
        if (!isDirectPublish) {
            if (!Objects.equals(abTestRemoteService.getUserType("invoice_type_abtest", userId), 1)) {
                throw new BusinessException(GoodsErrorCode.FAIL_PUBLISH_INVOICE_GOODS);
            }
        }

        if (StringUtils.isNotBlank(invoiceEnterpriseDO.getEnterpriseName())) {
            OnlineFreightEnterpriseDO onlineFreightEnterpriseDO = onlineFreightEnterpriseService.getByEnterpriseName(invoiceEnterpriseDO.getEnterpriseName());
            if (onlineFreightEnterpriseDO != null) {
                throw BusinessException.createException(GoodsErrorCode.FAIL_ENTERPRISE_CERTIFICATION.getCode(), "认证企业为网货平台，根据国家网络货运管理条例，网货平台不得相互委托业务");

            }
        }
    }

    /**
     * 校验开票货源发布参数
     */
    public void checkPublishInvoiceTransportParam(Long transportUserId, String distance, String price, String weight) {
        log.info("checkInvoiceTransportParam, transportUserId={}, distance={}, price={}, weight={}", transportUserId, distance, price, weight);
        try {
            InvoiceEnterpriseDO invoiceEnterpriseDO = invoiceEnterpriseService.getByCertigierUserId(transportUserId);
            Long enterpriseId = -1L;
            if (invoiceEnterpriseDO != null && invoiceEnterpriseDO.getId() != null) {
                enterpriseId = invoiceEnterpriseDO.getId();
            }

            InvoiceTransportConfigLogDTO lastInvoiceTransportEnterpriseConfig = invoiceTransportPriceEnterpriseConfigService.getLastInvoiceTransportEnterpriseConfig(enterpriseId, null);
            if (lastInvoiceTransportEnterpriseConfig == null) {
                return;
            }

            if (StringUtils.isNotBlank(lastInvoiceTransportEnterpriseConfig.getMaxPrice()) && StringUtils.isNotBlank(price)) {
                BigDecimal maxPrice = new BigDecimal(lastInvoiceTransportEnterpriseConfig.getMaxPrice()).multiply(new BigDecimal(10000));
                if (new BigDecimal(price).compareTo(maxPrice) > 0) {
                    throw BusinessException.createException(GoodsErrorCode.GOODS_PRICE_EXCEED_LIMIT.getCode(), GoodsErrorCode.GOODS_PRICE_EXCEED_LIMIT.getMsg().formatted(maxPrice));
                }
            }

            List<InvoiceTransportPriceConfigDTO> tytInvoiceTransportPriceConfigs = lastInvoiceTransportEnterpriseConfig.getTytInvoiceTransportPriceConfigList();
            if (!CollectionUtils.isEmpty(tytInvoiceTransportPriceConfigs)) {
                for (InvoiceTransportPriceConfigDTO config : tytInvoiceTransportPriceConfigs) {
                    checkInvoiceTransportPriceConfig(config, weight, distance, price);
                }
            }
        } catch (Exception e) {
            log.error("校验开票货源运价出错 transportUserId:{}, distance:{}, price:{}, weight:{}", transportUserId, distance, price, weight, e);
        }
    }

    /**
     * 校验开票货源运价
     */
    public void checkInvoiceTransportPriceConfig(InvoiceTransportPriceConfigDTO config, String weight, String distance, String price) {
        BigDecimal parsedWeight = StringUtils.isNotBlank(weight) ? new BigDecimal(weight) : null;
        BigDecimal priceBigDecimal = StringUtils.isNotBlank(price) ? new BigDecimal(price) : null;
        BigDecimal distanceBigDecimal = StringUtils.isNotBlank(distance) ? new BigDecimal(distance) : null;

        if (parsedWeight != null && (config.getLowTonnageValue() == null || parsedWeight.compareTo(new BigDecimal(config.getLowTonnageValue())) > 0) && (config.getHighTonnageValue() == null || parsedWeight.compareTo(new BigDecimal(config.getHighTonnageValue())) <= 0) && priceBigDecimal != null && distanceBigDecimal != null && (config.getLowDistanceValue() == null || distanceBigDecimal.compareTo(new BigDecimal(config.getLowDistanceValue())) > 0) && (config.getHighDistanceValue() == null || distanceBigDecimal.compareTo(new BigDecimal(config.getHighDistanceValue())) <= 0)) {
            if (config.getMaxPrice() == null) {
                if (priceBigDecimal.divide(distanceBigDecimal, 2, RoundingMode.DOWN).compareTo(new BigDecimal(config.getMaxDistanceUnitPrice())) > 0) {
                    throw new BusinessException(GoodsErrorCode.GOODS_PRICE_TOO_HIGH, null);
                }
            } else if (config.getMaxDistanceUnitPrice() == null) {
                if (priceBigDecimal.compareTo(new BigDecimal(config.getMaxPrice())) > 0) {
                    throw new BusinessException(GoodsErrorCode.GOODS_PRICE_TOO_HIGH, null);
                }
            } else if (priceBigDecimal.compareTo(new BigDecimal(config.getMaxPrice())) > 0 && priceBigDecimal.divide(distanceBigDecimal, 2, RoundingMode.DOWN).compareTo(new BigDecimal(config.getMaxDistanceUnitPrice())) > 0) {
                throw new BusinessException(GoodsErrorCode.GOODS_PRICE_TOO_HIGH, null);
            }
        }
    }

    /**
     * 获取开票货源企业税率和附加运费
     *
     * @param userId
     * @param price
     * @param invoiceSubjectId
     * @return
     */
    public InvoiceTransportTaxInfoDTO getAdditionalAndTaxRate(Long userId, String price, Long invoiceSubjectId) {
        if (userId == null || invoiceSubjectId == null) {
            throw new BusinessException(CommonErrorCode.ERROR_PARAMETER_LACK, null);
        }

        InvoiceTransportTaxInfoDTO transportTaxInfoDTO = new InvoiceTransportTaxInfoDTO();

        BigDecimal enterpriseTaxRate = DEFAULT_ENTERPRISE_TAX_RATE;
        // 调用接口根据userId和invoiceSubjectId获取开票费率
        try {
            WebResult<BigDecimal> taxRate = thirdEnterpriseRemoteService.getTaxRate(invoiceSubjectId, userId);
            if (taxRate != null && taxRate.ok()) {
                enterpriseTaxRate = taxRate.getData();
            }
        } catch (Exception e) {
            log.error("调用接口根据userId和invoiceSubjectId判断用户是否可以发开票货源失败，userId:{}, invoiceSubjectId:{}", userId, invoiceSubjectId, e);
            throw new BusinessException(GoodsErrorCode.FAIL_PUBLISH_INVOICE_GOODS, null);
        }
        transportTaxInfoDTO.setEnterpriseTaxRate(enterpriseTaxRate);

        if (!TransportUtil.hasPrice(price)) {
            return transportTaxInfoDTO;
        }

        BigDecimal taxRatePercentage = enterpriseTaxRate.divide(HUNDRED, 4, RoundingMode.HALF_UP);
        if (taxRatePercentage.compareTo(BigDecimal.ZERO) <= 0 || taxRatePercentage.compareTo(BigDecimal.ONE) >= 0) {
            throw new BusinessException(GoodsErrorCode.ENTERPRISE_TAX_ERROR, null);
        }
        BigDecimal priceNumber = new BigDecimal(price);
        BigDecimal additionalPrice = priceNumber.divide(BigDecimal.ONE.subtract(taxRatePercentage), 2, RoundingMode.UP).subtract(priceNumber);

        transportTaxInfoDTO.setAdditionalPrice(additionalPrice);

        return transportTaxInfoDTO;
    }

    public void checkXhlInvoiceTransport(PublishBO publishBO, UserRpcVO user) {
        if (Objects.equals(publishBO.getServiceProviderCode(), InvoiceServiceProviderEnum.XHL.getCode())) {
            //翔和翎开票校验收货人信息
            if (StringUtils.isBlank(publishBO.getConsigneeName()) || StringUtils.isBlank(publishBO.getConsigneeTel()) || StringUtils.isBlank(publishBO.getConsigneeEnterpriseName())) {
                throw new BusinessException(GoodsErrorCode.INVOICE_CONSIGNEE_INFO);
            }
            if (Objects.equals(user.getCellPhone(), publishBO.getConsigneeTel())) {
                throw BusinessException.createException(GoodsErrorCode.INVOICE_CONSIGNEE_INFO.getCode(), "收货联系电话不能与发货人账号一致");
            }
            if (Objects.equals(user.getTrueName(), publishBO.getConsigneeName())) {
                throw BusinessException.createException(GoodsErrorCode.INVOICE_CONSIGNEE_INFO.getCode(), "收货人姓名不能与发货人姓名一致");
            }

            InvoiceEnterpriseDO invoiceEnterprise = invoiceEnterpriseService.getByCertigierUserId(user.getId());
            if (invoiceEnterprise != null && StringUtils.isNotBlank(invoiceEnterprise.getEnterpriseName()) && Objects.equals(invoiceEnterprise.getEnterpriseName(), publishBO.getConsigneeEnterpriseName())) {
                throw BusinessException.createException(GoodsErrorCode.INVOICE_CONSIGNEE_INFO.getCode(), "收货单位不能与发货人企业认证名称一致");
            }

            if (publishBO.getConsigneeName().length() > 20) {
                throw BusinessException.createException(GoodsErrorCode.INVOICE_CONSIGNEE_INFO.getCode(), "收货人姓名超长");

            }
            if (publishBO.getConsigneeTel().length() > 20) {
                throw BusinessException.createException(GoodsErrorCode.INVOICE_CONSIGNEE_INFO.getCode(), "收货联系电话超长");

            }
            if (publishBO.getConsigneeEnterpriseName().length() > 64) {
                throw BusinessException.createException(GoodsErrorCode.INVOICE_CONSIGNEE_INFO.getCode(), "收货单位超长");

            }

        }
    }

}
