package com.teyuntong.goods.service.service.biz.transport.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainExtendDO;

import java.util.List;

/**
 * 客户资料服务类
 *
 * <AUTHOR>
 * @since 2024-01-18 17:34
 */
public interface TransportMainExtendService extends IService<TransportMainExtendDO> {

    /**
     * 根据源id查询扩展信息
     *
     * @param srcMsgId
     * @return
     */
    TransportMainExtendDO getBySrcMsgId(Long srcMsgId);

    List<TransportMainExtendDO> getBySrcMsgIds(List<Long> srcMsgIds);

    /**
     * 根据ID更新扩展信息
     *
     * @param mainExtend
     * @return
     */
    int updateMainExtendById(TransportMainExtendDO mainExtend);

    /**
     * 更新扩展信息
     *
     * @param mainExtend
     * @return
     */
    int updateMainExtend(TransportMainExtendDO mainExtend);
}
