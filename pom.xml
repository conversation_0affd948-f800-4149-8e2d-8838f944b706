<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.teyuntong</groupId>
        <artifactId>tyt-dependencies-spring-cloud</artifactId>
        <version>2021.0.9.0-********-1.1.0</version>
    </parent>
    <artifactId>tyt-goods-service</artifactId>
    <groupId>com.teyuntong</groupId>
    <version>1.0.4-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>tyt-goods-service</name>
    <description>货源服务</description>
    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <tyt-infra-common-bom.version>2.1.4-SNAPSHOT</tyt-infra-common-bom.version>
        <elasticsearch.version>7.14.0</elasticsearch.version>
        <easy-elasticsearch.version>2.0.0-beta4</easy-elasticsearch.version>
        <jieba-analysis.version>1.0.2</jieba-analysis.version>
        <tyt-basic-resource-client.version>1.0.0-SNAPSHOT</tyt-basic-resource-client.version>
        <tyt-outer-service-client.version>1.0.1-SNAPSHOT</tyt-outer-service-client.version>
        <tyt-inner-service-client.version>1.1.1-SNAPSHOT</tyt-inner-service-client.version>
        <tyt-user-trace-client.version>1.0-SNAPSHOT</tyt-user-trace-client.version>
        <tyt-market-activity-client.version>1.0-SNAPSHOT</tyt-market-activity-client.version>
        <dynamic-datasource-spring-boot-starter.version>3.5.0</dynamic-datasource-spring-boot-starter.version>
        <!-- ==================== sonar ==================== -->
        <tyt-p3c-pmd.version>1.0.0</tyt-p3c-pmd.version>
        <sonar.projectKey>${project.artifactId}</sonar.projectKey>
        <sonar.projectName>${project.artifactId}</sonar.projectName>
        <sonar.moduleKey>${project.artifactId}</sonar.moduleKey>
        <sonar.projectVersion>1.0</sonar.projectVersion>
        <sonar.inclusions>**/com/teyuntong/**/*.java,**/com/teyuntong/**/*.kt</sonar.inclusions>
        <sonar.java.binaries>target</sonar.java.binaries>
        <sonar.host.url>http://************:9000</sonar.host.url>
        <sonar.login>****************************************</sonar.login>

        <tyt-user-service-client.version>1.3.0-SNAPSHOT</tyt-user-service-client.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib-jdk8</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-infra-common-bom</artifactId>
                <version>${tyt-infra-common-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.easy-es</groupId>
                <artifactId>easy-es-boot-starter</artifactId>
                <version>${easy-elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>com.huaban</groupId>
                <artifactId>jieba-analysis</artifactId>
                <version>${jieba-analysis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-datasource-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-user-service-client</artifactId>
                <version>${tyt-user-service-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-infra-basic-resource-client</artifactId>
                <version>${tyt-basic-resource-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-inner-export-service-client</artifactId>
                <version>${tyt-inner-service-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-user-trace-client</artifactId>
                <version>${tyt-user-trace-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-outer-export-service-client</artifactId>
                <version>${tyt-outer-service-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-market-activity-client</artifactId>
                <version>${tyt-market-activity-client.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <plugins>
            <!-- maven 编译插件 集成了 lombok 和 mapstruct, 不要乱改 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <!-- 一键修改版本号插件 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
            </plugin>

            <!-- maven resources插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
            </plugin>
            <!-- 执行JUnit或者TestNG的测试用例的插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>

            <!-- sonar插件 -->
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
            </plugin>


            <!-- maven 代码静态分析的插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <dependencies>
                    <dependency>
                        <groupId>com.tyt</groupId>
                        <artifactId>tyt-p3c-pmd</artifactId>
                        <version>${tyt-p3c-pmd.version}</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <id>pmd</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <printFailingErrors>true</printFailingErrors>
                    <minimumPriority>1</minimumPriority>
                    <rulesets>
                        <ruleset>/rulesets/java/tyt-p3c-pmd.xml</ruleset>
                    </rulesets>
                    <includes>
                        <include>**/com/teyuntong/**/*.java</include>
                        <include>**/com/teyuntong/**/*.kt</include>
                    </includes>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <modules>
        <module>tyt-goods-service-client</module>
        <module>tyt-goods-service-service</module>
        <module>tyt-goods-service-schedule</module>
        <module>tyt-goods-service-mq</module>
        <module>tyt-goods-service-starter</module>
        <module>tyt-goods-service-mybatis-generator</module>
    </modules>
</project>
