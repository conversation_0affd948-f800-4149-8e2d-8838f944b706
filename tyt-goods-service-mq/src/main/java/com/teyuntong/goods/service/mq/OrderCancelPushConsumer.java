package com.teyuntong.goods.service.mq;

import cn.hutool.json.JSONUtil;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.teyuntong.goods.service.service.mq.consumer.OrderCancelPushConsumeService;
import com.teyuntong.goods.service.service.mq.pojo.OrderCancelMqBean;
import com.teyuntong.infra.common.rocketmq.annotation.RocketMqMessageListener;
import com.teyuntong.infra.common.rocketmq.listener.CovertBodyMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 订单撤销后发送推荐货源的消息
 *
 * <AUTHOR>
 * @since 2025-02-20 14:43
 */
@Slf4j
@Component
@RocketMqMessageListener(consumerGroup = "GID_GOODS_CENTER_TOPIC_ORDER_CANCEL", consumeThreadNums = 5)
public class OrderCancelPushConsumer extends CovertBodyMessageListener<OrderCancelMqBean> {
    public OrderCancelPushConsumer() {
        super(OrderCancelMqBean.class);
    }

    @Resource
    private OrderCancelPushConsumeService orderCancelPushConsumeService;

    @Override
    protected Action consume(Message message, OrderCancelMqBean orderCancelMqBean, ConsumeContext consumeContext) {
        try {
            log.error("撤销订单mq 消费开始, msg bean:{}", JSONUtil.toJsonStr(orderCancelMqBean));
            orderCancelPushConsumeService.consume(orderCancelMqBean);
            return Action.CommitMessage;
        } catch (Exception e) {
            log.error("撤销订单推送货源 error, msg bean:{}", JSONUtil.toJsonStr(orderCancelMqBean), e);
            return Action.ReconsumeLater;
        }
    }
}
