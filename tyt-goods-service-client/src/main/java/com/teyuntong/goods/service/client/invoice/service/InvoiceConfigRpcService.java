package com.teyuntong.goods.service.client.invoice.service;

import com.teyuntong.goods.service.client.invoice.vo.InvoiceTransportConfigLogVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 开票配置RPC服务接口
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
public interface InvoiceConfigRpcService {

    /**
     * 获取企业开票运输配置
     *
     * @param enterpriseId 企业ID
     * @param serviceProviderCode 服务提供商代码
     * @return 开票运输配置信息
     */
    @GetMapping(value = "/rpc/invoice/config/getLastInvoiceTransportEnterpriseConfig")
    InvoiceTransportConfigLogVO getLastInvoiceTransportEnterpriseConfig(
            @RequestParam("enterpriseId") Long enterpriseId,
            @RequestParam("serviceProviderCode") String serviceProviderCode);
}
