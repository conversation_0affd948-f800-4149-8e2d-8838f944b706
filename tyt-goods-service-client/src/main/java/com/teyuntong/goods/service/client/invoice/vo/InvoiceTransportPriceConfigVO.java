package com.teyuntong.goods.service.client.invoice.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 开票运输价格配置VO
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Getter
@Setter
public class InvoiceTransportPriceConfigVO {

    private Long id;

    /**
     * 吨位高值
     */
    private Integer highTonnageValue;

    /**
     * 吨位低值
     */
    private Integer lowTonnageValue;

    /**
     * 距离高值
     */
    private Integer highDistanceValue;

    /**
     * 距离低值
     */
    private Integer lowDistanceValue;

    /**
     * 运费上线（元）
     */
    private Integer maxPrice;

    /**
     * 车公里单价（元）
     */
    private Integer maxDistanceUnitPrice;

    /**
     * 创建时间
     */
    private Date createTime;
}
