package com.teyuntong.goods.service.client.publish.service;

import com.teyuntong.goods.service.client.publish.dto.DirectPublishDTO;
import com.teyuntong.goods.service.client.transport.vo.DirectPublishResultVO;
import com.teyuntong.goods.service.client.publish.dto.UpdateGoodsInfoDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 货源直接发布接口
 *
 * <AUTHOR>
 * @since 2024/12/12 13:40
 */
public interface TransportDirectPublishRpcService {

    /**
     * 直接发布
     */
    @PostMapping(value = "/rpc/transport/direct/publish/directPublish")
    DirectPublishResultVO directPublish(@RequestBody DirectPublishDTO directPublishDTO);

    /**
     * 填价
     */
    @PostMapping(value = "/rpc/transport/direct/publish/fillPrice")
    DirectPublishResultVO fillPrice(@RequestBody DirectPublishDTO directPublishDTO);

    /**
     * 加价
     */
    @PostMapping(value = "/rpc/transport/direct/publish/addPrice")
    DirectPublishResultVO addPrice(@RequestBody DirectPublishDTO directPublishDTO);

    /**
     * 填价/加价接口
     */
    @PostMapping(value = "/rpc/transport/direct/publish/updatePrice")
    DirectPublishResultVO updatePrice(@RequestBody DirectPublishDTO directPublishDTO);

    /**
     * 转电议/一口价
     */
    @PostMapping(value = "/rpc/transport/direct/publish/transfer")
    DirectPublishResultVO transfer(@RequestBody DirectPublishDTO directPublishDTO);

    /**
     * 曝光
     */
    @PostMapping(value = "/rpc/transport/direct/publish/rePublish")
    DirectPublishResultVO rePublish(@RequestBody DirectPublishDTO directPublishDTO);

    /**
     * 更新货源信息
     */
    @PostMapping(value = "/rpc/transport/direct/publish/updateGoodsInfo")
    DirectPublishResultVO updateGoodsInfo(@RequestBody UpdateGoodsInfoDTO updateGoodsInfoDTO);

    /**
     * 自动重发货源
     */
    @GetMapping(value = "/rpc/transport/direct/publish/autoResend")
    DirectPublishResultVO autoResend(@RequestBody DirectPublishDTO directPublishDTO);
}
