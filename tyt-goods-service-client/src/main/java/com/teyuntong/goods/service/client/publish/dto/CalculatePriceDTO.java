package com.teyuntong.goods.service.client.publish.dto;

import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024-06-03 15:05
 */
@Data
public class CalculatePriceDTO {
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 公里数
     */
    @NotNull(message = "公里数不能为空")
    @DecimalMax(value = "10000", message = "公里数最大值为10000")
    @DecimalMin(value = "0.1", message = "公里数最小值为0.1")
    private BigDecimal distanceKilometer;

    /**
     * 其他费用
     */
    private BigDecimal otherFee;

    /**
     * 司机驾驶此类货物：1-需要，2-不需要
     */
    private Integer driverDriving;

    /**
     * 用车类型：1-整车，2-零担
     */
    private Integer useCarType;

    /**
     * 运费所属企业ID
     */
    private Long cargoOwnerId;

    /**
     * 出发地
     */
    @NotEmpty(message = "出发地不能为空")
    private String startCity;

    /**
     * 目的地
     */
    @NotEmpty(message = "目的地不能为空")
    private String destCity;

    /**
     * 重量
     */
    @NotEmpty(message = "重量不能为空")
    private String weight;

    /**
     * 订金（选择定金不退还时，订金要加到运费中）
     */
    private BigDecimal infoFee;

    /**
     * 灵活运价的运价下限（百分比）
     */
    private BigDecimal lowerLimit;

    /**
     * 驾驶能力费用
     */
    private BigDecimal drivingFee;

    /**
     * 版本号
     */
    private String clientVersion;
}
