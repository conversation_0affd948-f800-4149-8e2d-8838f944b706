package com.teyuntong.goods.service.client.transport.service;

import com.teyuntong.goods.service.client.transport.dto.CustomInformationDTO;
import com.teyuntong.goods.service.client.transport.dto.TransportBackendDTO;
import com.teyuntong.goods.service.client.transport.dto.TransportDispatchDTO;
import com.teyuntong.goods.service.client.transport.vo.CustomInformationVO;
import com.teyuntong.goods.service.client.transport.vo.TransportBackendVO;
import com.teyuntong.goods.service.client.transport.vo.TransportDispatchVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 运输代调管理服务接口
 *
 * <AUTHOR>
 * @since 2024-01-17 16:08
 */
public interface CustomInformationRpcService {

    /**
     * 查询客户资料信息
     *
     * @param customInformationDTO
     * @return
     */
    @PostMapping(value = "/rpc/transport/custom/query", consumes = "application/json")
    CustomInformationVO query(@RequestBody @Validated CustomInformationDTO customInformationDTO);

    /**
     * 根据手机号查询客户资料信息
     *
     * @param goodsPhone
     * @return
     */
    @GetMapping("/rpc/transport/custom/queryByPhone")
    CustomInformationVO queryByPhone(@RequestParam("goodsPhone") String goodsPhone);

    /**
     * 修改最近一次成单时间
     *
     * @param goodsPhone
     * @param tsId
     */
    @GetMapping("/rpc/transport/custom/modifyPerformanceTime")
    void modifyPerformanceTime(@RequestParam("goodsPhone") String goodsPhone, @RequestParam("tsId") Long tsId);
}
