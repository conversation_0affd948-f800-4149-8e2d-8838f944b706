package com.teyuntong.goods.service.client.publish.dto;

import lombok.Data;

/**
 * 回价助手
 *
 * <AUTHOR>
 * @since 2025-05-29 15:47
 */
@Data
public class PriceAssistantDTO {
    /**
     * 发货类型：0-普货，1-优车，2-专车
     */
    private Integer excellentGoods;

    /**
     * 是否专票货源 0：否；1:是
     */
    private Integer invoiceTransport;

    /**
     * 货源ID（编辑发布和再发一单时传参）
     */
    private Long srcMsgId;

    /**
     * 运费上限
     */
    private Integer priceCap;
}
