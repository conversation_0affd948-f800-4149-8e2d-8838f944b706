package com.teyuntong.goods.service.client.quotedprice.vo;

import lombok.Data;

import java.util.List;

/**
    if (finalQuotedPriceIsDone == 1) {
        //报价已被接受，页面展示 finalQuotedPrice 最终报价字段
    } else if (!carIsQuotedPriceAgain) {
        //车方未响应货方出价
    } else if (canQuotedPrice) {
        //车方驳回了货方的出价，并已回价
        //此时货方允许出价
        //页面展示 carQuotedPrice 车方方报价相关字段，并展示驳回、同意两个按钮
    } else {
        //货方不允许出价了
        //页面展示 carQuotedPrice 车方报价相关字段，并只展示同意按钮
    }
 */
@Data
public class TransportQuotedPriceTransportVO extends TransportQuotedPriceVO {

    /**
     * 车方是否回价
     */
    private Boolean carIsQuotedPriceAgain;

    /**
     * 货方是否可出价
     */
    private Boolean canQuotedPrice;

    /**
     * 货方在货源详情顶部展示的报价记录是否有 新 标签
     */
    private Boolean transportNoLook = false;

    //是否会员
    private Integer carIsVip;

    //头像，已拼好
    private String headUrl;

    //是否实名认证
    private Boolean realNameAuthentication;

    private String price;

    /**
     * 车方信用等级
     */
    private String carCreditRankLevel;

    /**
     * 好评率
     */
    private String rating;

    /**
     * 好评率展示类型：
     * 0：不展示好评率
     * 1：评价量≥3条时,展示好评率
     * 2：0＜评价量＜3，且有好评，展示近期有好评
     * 3：0＜评价量＜3，且无好评，展示近期无好评
     * 4：评价量=0，展示暂无评价
     */
    private Integer ratingType;

    /**
     * 好评数
     */
    private Long positiveCount;

    /**
     * 货物类型
     */
    private String goodsTypeName;

    /**
     * 承运货物次数
     */
    private Integer goodsTypeNum = 0;


    /**
     * 好评标签名，最多2条
     */
    private List<String> positiveLabels;

    /**
     * 差评标签名，最多1条
     */
    private List<String> negativeLabels;

    /**
     * 平台交易数
     */
    private String tradeNums;

    /**
     * 与我交易数
     */
    private String coopNums;

    /**
     * 是否显示给司机拨打电话按钮 0:不显示；1:显示
     */
    private Integer haveCallToCarButton = 0;

}