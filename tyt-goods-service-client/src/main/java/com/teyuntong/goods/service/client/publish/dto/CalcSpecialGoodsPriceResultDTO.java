package com.teyuntong.goods.service.client.publish.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 专车运费测算_V2
 *
 * <AUTHOR>
 * @since 2025/02/18 15:03
 */
@Getter
@Setter
public class CalcSpecialGoodsPriceResultDTO {

    /**
     * 运费
     */
    private BigDecimal price;

    /**
     * 运费为0时的提示信息
     */
    private String notice;

    /**
     * 运价模式：1-固定运价，2-灵活运价
     */
    private Integer priceType;

    /**
     * 运价下限（10.5%）
     */
    private BigDecimal lowerLimit;

    /**
     * 允许修改的最低价格
     */
    private BigDecimal lowerLimitPrice;

    /**
     * 专车最快接单时间
     */
    private Integer fastDispatchMin;

    /**
     * 专车最快接单时间文案
     */
    private String fastDispatchWord;

    /**
     * 是否强制置顶并选中 1：是；2：否
     */
    private Integer specialCarTabTopAndCheck;

    /**
     * 优惠金额
     */
    private BigDecimal perkPrice;

}
