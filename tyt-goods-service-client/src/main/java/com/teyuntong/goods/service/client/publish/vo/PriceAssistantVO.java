package com.teyuntong.goods.service.client.publish.vo;

import lombok.Data;

/**
 * 回价助手
 *
 * <AUTHOR>
 * @since 2025-05-29 15:49
 */
@Data
public class PriceAssistantVO {
    /**
     * 是否展示回价助手：0-否，1-是
     */
    private Integer showPriceAssistant = 0;

    /**
     * 标签
     */
    private String label;

    /**
     * 描述
     */
    private String description;

    /**
     * 运费上限, (编辑发布和再发一单时返回)
     */
    private Integer  priceCap;

    /**
     * 弹窗描述
     */
    private String popupDescription;

    /**
     * 默认填充运费比例(%)
     */
    private Integer addPriceRate;
}
