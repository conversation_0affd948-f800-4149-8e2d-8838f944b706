package com.teyuntong.goods.service.client.publish.service;

import com.teyuntong.goods.service.client.publish.dto.PriceAssistantDTO;
import com.teyuntong.goods.service.client.publish.vo.PriceAssistantVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 回价助手接口
 *
 * <AUTHOR>
 * @since 2025-05-29 15:56
 */
public interface PriceAssistantRpcService {
    /**
     * 是否展示回价助手
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/price/assistant/showPriceAssistant")
    PriceAssistantVO showPriceAssistant(@RequestBody PriceAssistantDTO dto);

    /**
     * 保存回价助手
     *
     * @param dto
     */
    @PostMapping(value = "/price/assistant/savePriceAssistant")
    Boolean savePriceAssistant(@RequestBody PriceAssistantDTO dto);
}
