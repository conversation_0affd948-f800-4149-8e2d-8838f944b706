package com.teyuntong.goods.service.client.transport.service;

import com.teyuntong.goods.service.client.transport.dto.*;
import com.teyuntong.goods.service.client.transport.vo.*;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;

/**
 * 运输代调管理服务接口
 *
 * <AUTHOR>
 * @since 2024-01-17 16:08
 */
public interface TransportMainRpcService {

    /**
     * 查货源信息
     *
     * @param srcMsgIdList 货源id
     * @return List<TransportMainVO>
     */
    @GetMapping(value = "/rpc/transportMain/queryByIds")
    List<TransportMainVO> queryByIds(@RequestParam(value = "srcMsgIdList", required = false) List<Long> srcMsgIdList);

    /**
     * 查货源信息
     *
     * @param srcMsgId 货源id
     * @return List<TransportMainVO>
     */
    @GetMapping(value = "/rpc/transportMain/queryById")
    TransportMainVO queryById(@RequestParam(value = "srcMsgId", required = false) Long srcMsgId);

    /**
     * 获取货主发布中的开票货源ID集合
     *
     * @param userId 货主ID
     * @return 货源ID
     */
    @GetMapping(value = "/rpc/getInReleaseInvoiceTransportSrcMsgIdList")
    List<Long> getInReleaseInvoiceTransportSrcMsgIdList(@RequestParam("userId") Long userId);

    /**
     * 用户是否是货源货主
     *
     * @param userId
     * @param srcMsgId
     * @return
     */
    @GetMapping(value = "/rpc/checkUserIsTransportOwner")
    Boolean checkUserIsTransportOwner(@RequestParam("userId") Long userId, @RequestParam("srcMsgId") Long srcMsgId);

    /**
     * 货源列表
     *
     * @return
     */
    @PostMapping(value = "/rpc/getMyPublish")
    MyTransportVO getMyPublish(@RequestBody TransportListDTO dto);

    /**
     * 获取货主发布中的开票货源ID集合
     *
     * @param userId 货主ID
     * @return 货源ID
     */
    @GetMapping(value = "/rpc/getTransportDynamic")
    TransportDynamicVO getTransportDynamic(@RequestParam("userId") Long userId);

    @GetMapping(value = "/rpc/checkIsNeedFreeTecSericeFeeByCarUser")
    CheckIsNeedFreeTecServiceFeeVO checkIsNeedFreeTecSericeFeeByCarUser(@RequestParam("carUserId") Long carUserId, @RequestParam("srcMsgId") Long srcMsgId);

    @GetMapping(value = "/rpc/checkIsNeedFreeTecSericeFeeByTransport")
    CheckIsNeedFreeTecServiceFeeVO checkIsNeedFreeTecSericeFeeByTransport(@RequestParam("transportUserId") Long transportUserId, @RequestParam("startCity") String startCity, @RequestParam("isGoodCarPriceTransport") boolean isGoodCarPriceTransport);

    @GetMapping(value = "/rpc/getLastTransportInvoiceSubjectId")
    Long getLastTransportInvoiceSubjectId(@RequestParam("userId") Long userId);

    @GetMapping(value = "/rpc/initRecordGoodsTransactionInfo")
    void initRecordGoodsTransactionInfo(@RequestParam("srcMsgId") Long srcMsgId);

    /**
     * 获取发货锚点和发货方式
     *
     * @param goodsPointDTO
     * @return
     */
    @PostMapping("/rpc/transportMain/getGoodsPoint")
    GoodsPointResultDTO getGoodsPoint(@RequestBody GoodsPointDTO goodsPointDTO);

    /**
     * 校验是否满足拼车
     *
     * @param goodsPointDTO
     * @return
     */
    @PostMapping("/rpc/transportMain/checkMatchCarpool")
    Boolean checkMatchCarpool(@RequestBody GoodsPointDTO goodsPointDTO);

    /**
     * 查询扩展货源信息
     */
    @GetMapping(value = "/rpc/transportMain/getExtendBySrcMsgId")
    TransportMainExtendVO getExtendBySrcMsgId(@RequestParam("srcMsgId") Long srcMsgId);

    /**
     * 查询扩展货源信息
     */
    @GetMapping(value = "/rpc/transportMain/getExtendBySrcMsgIds")
    List<TransportMainExtendVO> getExtendBySrcMsgIds(@RequestParam("srcMsgIdList") List<Long> srcMsgIdList);

    /**
     * 查询相似货源数量
     */
    @PostMapping(value = "/rpc/transportMain/getSimilarityCount")
    int getSimilarityCount(@RequestBody TransportSimilarityDTO similarityDTO);

    /**
     * 根据userId查询货源
     */
    @PostMapping(value = "/rpc/transportMain/getTransportCount")
    int getTransportCountForUserId(@RequestBody TransportCountDTO transportCountDTO);


    /**
     * 获取用户最近30天（不包含今天）最后一天发货当天的所有货源
     *
     * @param userId 用户id
     * @return list
     */
    @GetMapping(value = "/rpc/getLastDayTransport")
    List<TransportMainVO> getLastDayTransport(@RequestParam("userId") Long userId);

    /**
     * 获取用户发货次数
     *
     * @param userId    用户id
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return int
     */
    @GetMapping(value = "/rpc/transport/main/getPublishCountByUserId")
    Integer getPublishCountByUserId(@RequestParam("userId") Long userId, @RequestParam("startTime") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime, @RequestParam("endTime") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime);

    /**
     * 获取用户发布中的抽佣货源
     *
     * @param userId 用户id
     * @return list
     */
    @GetMapping(value = "/rpc/transport/main/getUserCommissionGoods")
    List<TransportMainVO> getUserCommissionGoods(@RequestParam("userId") Long userId);

    /**
     * 判断货源是否是超额保障标签
     *
     * @param srcMsgId 货源id
     * @return true/false
     */
    @GetMapping(value = "/rpc/transport/main/isExcessGuaranteeTransport")
    Boolean isExcessGuaranteeTransport(@RequestParam("srcMsgId") Long srcMsgId);

    /**
     * 查询相似货源数量
     */
    @PostMapping(value = "/rpc/transportMain/getSimilarityTransportTurnoverRatio")
    SimilarityTransportTurnoverRatioVO getSimilarityTransportTurnoverRatio(@RequestBody TransportSimilarityDTO similarityDTO);

    @PostMapping(value = "/rpc/transportMain/getSimilarityTransportHavePriceCount")
    Boolean getSimilarityTransportHavePriceCount(@RequestParam("similarityCode") String similarityCode);

    /**
     * 删除我的非发布中的货源
     */
    @GetMapping(value = "/rpc/transport/deleteMyGoods")
    void deleteMyGoods(@RequestParam("srcMsgId") Long srcMsgId);

    /**
     * 获取用户当天发布的货源信息
     */
    @GetMapping(value = "/rpc/transport/getByUserId")
    List<Long> getTransportForUser(@RequestParam("userId") Long userId,
                                   @RequestParam(value = "status", required = false) Integer status);
}
