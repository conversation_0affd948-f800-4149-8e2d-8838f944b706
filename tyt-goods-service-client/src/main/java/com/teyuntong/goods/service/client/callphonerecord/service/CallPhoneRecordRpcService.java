package com.teyuntong.goods.service.client.callphonerecord.service;

import com.teyuntong.goods.service.client.callphonerecord.vo.GetCarPhoneVo;
import com.teyuntong.goods.service.client.callphonerecord.vo.TransportRecordVo;
import com.teyuntong.goods.service.client.callphonerecord.vo.CallPhoneRecordVo;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;


public interface CallPhoneRecordRpcService {

    /**
     * 通话记录接口
     */
    @GetMapping(value = "/rpc/callPhoneRecord/transportList")
    List<TransportRecordVo> transportList(@RequestParam("userId") Long userId);

    /**
     * 返回拨打次数
     *
     * @param srcMsgId 货源srcMsgId
     * @param userId   车端用户id
     * @return 拨打次数，默认为0
     */
    @GetMapping(value = "/rpc/callPhoneRecord/getCallCount")
    Integer getCallCount(@RequestParam(value = "srcMsgId") Long srcMsgId,
                         @RequestParam(value = "userId", required = false) Long userId);

    /**
     * 返回拨打次数
     *
     * @param userId    车用户id
     * @param startTime 开始时间
     * @param endTime   截止时间
     * @return 拨打次数，默认为0
     */
    @GetMapping(value = "/rpc/callPhoneRecord/getCallCountOfPeriod")
    Integer getCallCountOfPeriod(@RequestParam(value = "userId") Long userId,
                                 @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                 @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime);

    /**
     * 获取用户拨打次数
     *
     * @param userId    用户id
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 拨打次数
     */
    @GetMapping(value = "/rpc/callPhoneRecord/getUserCallPhoneCount")
    Integer getUserCallPhoneCount(@RequestParam(value = "userId") Long userId,
                                  @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                  @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime);


    /**
     * 获取用户手机号（货方拨打车方电话时调用）
     *
     * @param linkUserId 要拨打的用户id
     * @return 拨打次数
     */
    @GetMapping(value = "/rpc/call/getPhone")
    GetCarPhoneVo getPhone(@RequestParam(value = "linkUserId") Long linkUserId);

    /**
     * 获取用户手机号（货方拨打车方电话时调用）
     *
     * @param linkUserId 要拨打的用户id
     * @return 拨打次数
     */
    @GetMapping(value = "/rpc/call/getPhoneNoAuth")
    GetCarPhoneVo getPhoneNoAuth(@RequestParam(value = "linkUserId") Long linkUserId);

    /**
     * 意向车源
     * @param srcMsgId 货源ID
     * @param userId 货主ID
     * @return 意向车源列表
     */
    @GetMapping("/rpc/call/phone/record/recent/callList")
    List<CallPhoneRecordVo> contactedList(@RequestParam("srcMsgId") Long srcMsgId, @RequestParam("userId") Long userId);


}
