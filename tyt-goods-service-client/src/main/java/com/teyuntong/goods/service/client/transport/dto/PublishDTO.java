package com.teyuntong.goods.service.client.transport.dto;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/1/3
 */
@Data
public class PublishDTO {

//    =========客户端传入参数===========

    /**
     * 货源id
     */
    private Long srcMsgId;
    /**
     * 货源来源
     * normal(1, "货主"),
     * dispatch(2, "调度客服"),
     * goods_owner(3, "个人货主"),
     * ymm(4, "运满满"),
     */
    private Integer sourceType;

    /**
     * 是否开票货源 0：否；1：是
     */
    @NotNull(message = "是否开票货源不能为空")
    private Integer invoiceTransport;

    /**
     * 订金类型（0不退还；1退还）,默认0不退还
     */
    private Integer refundFlag = 0;

    /**
     * 开票货源指派车方联系方式
     */
    private String assignCarTel;
    /**
     * 订金金额（元）
     */
    private BigDecimal infoFee;
    /**
     * 货物内容
     */
    @NotBlank(message = "货物内容不能为空")
    private String taskContent;
    /**
     * 货物重量（吨）
     */
    @NotBlank(message = "货物重量不能为空")
    private String weight;
    /**
     * 出发地省
     */
    @NotBlank(message = "出发地省不能为空")
    private String startProvinc;
    /**
     * 出发地市
     */
    @NotBlank(message = "出发地市不能为空")
    private String startCity;
    /**
     * 出发地区/县
     */
    private String startArea;
    /**
     * 出发地详细地址
     */
    private String startDetailAdd;
    /**
     * 出发地省市区
     */
    private String startPoint;

    /**
     * 出发地x坐标
     */
    private BigDecimal startCoordX;
    /**
     * 出发地y坐标
     */
    private BigDecimal startCoordY;

    /**
     * 目的地省
     */
    @NotBlank(message = "目的地省不能为空")
    private String destProvinc;
    /**
     * 目的地市
     */
    @NotBlank(message = "目的地市不能为空")
    private String destCity;
    /**
     * 目的地区/县
     */
    private String destArea;
    /**
     * 目的地详细地址
     */
    private String destDetailAdd;
    /**
     * 目的地省市区
     */
    private String destPoint;
    /**
     * 出发地经度
     */
    @NotNull(message = "出发地经度不能为空")
    @Min(message = "出发地经度不能小于0", value = 0L)
    private BigDecimal startLongitude;
    /**
     * 出发地纬度
     */
    @NotNull(message = "出发地纬度不能为空")
    @Min(message = "出发地纬度不能小于0", value = 0L)
    private BigDecimal startLatitude;
    /**
     * 目的地经度
     */
    @NotNull(message = "目的地经度不能为空")
    @Min(message = "目的地经度不能小于0", value = 0L)
    private BigDecimal destLongitude;
    /**
     * 目的地纬度
     */
    @NotNull(message = "目的地纬度不能为空")
    @Min(message = "目的地纬度不能小于0", value = 0L)
    private BigDecimal destLatitude;

    /**
     * 联系人电话
     */
    @NotBlank(message = "联系人电话不能为空")
    private String tel;
    /**
     * 联系人电话3
     */
    private String tel3;
    /**
     * 联系人电话4
     */
    private String tel4;

    /**
     * 签约合作商ID
     */
    private Long cargoOwnerId;
    /**
     * 其他费用
     */
    private BigDecimal otherFee;

    /**
     * 是否是优推好车主  1：是  0 否
     */
    private Integer priorityRecommend;

    /**
     * 是否是优车货源（0-否，1-优车，2-专车）
     */
    private Integer excellentGoods;

    /**
     * 司机驾驶此类货物：1-需要，2-不需要
     */
    private Integer driverDriving;

    /**
     * 用车类型：1-整车，2-零担
     */
    private Integer useCarType;

    /**
     * 公里数（专车发货）
     */
    private BigDecimal distanceKilometer;

    /**
     * 满满货源版本号
     */
    private Integer cargoVersion;


    /**
     * 运满满货源唯一标识
     */
    private Long cargoId;

    /**
     * 是否优车运价货源(优车2.0) 1:是 0:否
     */
    private Integer goodCarPriceTransport;

    /**
     * 调车数量
     */
    private Integer shuntingQuantity;
    /**
     * 开票货源开票主体ID
     */
    private Long invoiceSubjectId;

    /**
     * 服务商编码
     */
    private String serviceProviderCode;

    /**
     * XHL开票-收货人姓名
     */
    private String consigneeName;

    /**
     * XHL开票-收货人联系方式
     */
    private String consigneeTel;

    /**
     * XHL开票-收货人企业名称
     */
    private String consigneeEnterpriseName;
    /**
     * 是否自动重发：1是0否
     */
    private Integer isAutoResend;

    /**
     * 联系人名称
     */
    private String linkMan;
    /**
     * 备注信息
     */
    private String remark;
    /**
     * 货物型号
     */
    private String type;
    /**
     * 货物品牌
     */
    private String brand;
    /**
     * 货类
     */
    private String goodTypeName;
    /**
     * 是否是标准化数据：0是，1不是
     */
    private Integer isStandard;
    /**
     * 货物的台数，针对标准化的数据
     */
    private Integer goodNumber;
    /**
     * 标准化货源id
     */
    private Integer matchItemId;
    /**
     * 重发时间
     */
    private String resend;

    // 所需车辆类型/长度/特殊需求
    private String carType;
    private String carLength;
    private String specialRequired;
    /**
     * 价格
     */
    private String price;


    // 距离/重量/长度/宽/高/是否超重
    private BigDecimal distance;

    private String length;
    private String wide;
    private String high;
    /**
     * 是否三超
     */
    private String isSuperelevation;

    /**
     * 出发地行政编码
     */
    private String startAdcode;

    /**
     * 目的地行政编码
     */
    private String destAdcode;//目的地行政编码


    private Integer isInfoFee;


    /**
     * 目的地X坐标
     */
    private BigDecimal destCoordX;
    /**
     * 目的地Y坐标
     */
    private BigDecimal destCoordY;


    private String basePrice;
    private String profixRate;

    //车货拆分新增参数
    /**
     * 开始装车时间
     */
    private Date beginLoadingTime;
    /**
     * 装车时间
     */
    private Date loadingTime;
    /**
     * 开始卸车时间
     */
    private Date beginUnloadTime;
    /**
     * 卸车时间
     */
    private Date unloadTime;
    /**
     * 车辆最小长度
     */
    private BigDecimal carMinLength;
    /**
     * 车辆最大长度
     */
    private BigDecimal carMaxLength;
    /**
     * 挂车样式
     */
    private String carStyle;
    /**
     * 工作面高最小值
     */
    private BigDecimal workPlaneMinHigh;
    /**
     * 工作面高最大值
     */
    private BigDecimal workPlaneMaxHigh;
    /**
     * 工作面长最小值
     */
    private BigDecimal workPlaneMinLength;
    /**
     * 工作面长最大值
     */
    private BigDecimal workPlaneMaxLength;
    /**
     * 是否需要带爬梯 0 是 1 需要 2不需要
     */
    private String climb;
    /**
     * 是否货主接单货源 1是 2和空或者其他为否
     */
    private Long backendId;
    /**
     * 所需车辆长度标签
     */
    private String carLengthLabels;

    /**
     * * 轮胎外露标识 0不限 1是 2 否
     */
    private String tyreExposedFlag;
    /**
     * 发货类型 1：电议  2：一口价
     */
    @NotNull(message = "发货类型不能为空")
    private Integer publishType;

    private Integer busPublishType; //1推熟车  2.平台找车
    private Long friendCarUserId; //推熟车的用户id
    /**
     * 是否是小程序货源 0：否 1：是
     */
    private Integer isBackendTransport;


    /**
     * s是否有货源保障标识（0否；1是）
     */
    private Integer guaranteeGoods;

    /**
     * 重货确认（确认时传1）
     */
    private Integer confirm;


    /**
     * 装货联系电话
     */
    private String loadCellPhone;

    /**
     * 卸货联系电话
     */
    private String unloadCellPhone;


    /**
     * 技术服务费
     */
    private BigDecimal tecServiceFee;

    /**
     * 运满满货源真实货主电话
     */
    private String giveGoodsPhone;

    /**
     * 运满满货源真实货主名称
     */
    private String contactName;

    /**
     * 标准货名备注
     */
    private String machineRemark;

    /**
     * 1：待审核2：合规货名  3：非法货名
     */
    private Integer machineType;

    /**
     * 优车发货卡id,没有时传空.tyt_excellent_goods_card_user_detail表的id
     */
    private Long excellentCardId;


    /**
     * 附加运费
     */
    private String additionalPrice;

    /**
     * 企业税率
     */
    private BigDecimal enterpriseTaxRate;

    /**
     * 发货时是否展示了优车好货（埋点专用参数）
     */
    private Integer publishTransportIsShowGoodCar;

    /**
     * 出发地地址来源,1:地图选点，2：手动搜索
     */
    private Integer startAddrSource;
    /**
     * 目的地地址来源     1:地图选点，2：手动搜索
     */
    private Integer destAddrSource;

    /**
     * 建议价最小价格
     */
    private Integer thMinPrice;

    /**
     * 建议价最大价格建议价格
     */
    private Integer thMaxPrice;

    /**
     * 建议价建议价格
     */
    private Integer suggestPrice;

    /**
     * 推荐最低价
     */
    private Integer suggestMinPrice;

    //推荐最高价
    private Integer suggestMaxPrice;

    /**
     * 回价助手运费上限
     */
    private Integer priceCap;

    //优车定价低值
    private Integer fixPriceMin;

    //优车定价高值
    private Integer fixPriceMax;

    //优车定价最快成交价格
    private Integer fixPriceFast;

    //是否弹出了优车定价货源卡片
    private Integer showGoodCarPriceTransportTab;
    /**
     * 预付运费
     */
    private BigDecimal prepaidPrice;

    /**
     * 到付运费
     */
    private BigDecimal collectedPrice;

    /**
     * 回单付运费
     */
    private BigDecimal receiptPrice;
    /**
     * 0：全额支付  1：分段支付：
     */
    private Integer paymentsType;

    /**
     * 优惠价格
     */
    private BigDecimal perkPrice;


}
