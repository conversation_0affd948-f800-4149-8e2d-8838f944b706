package com.teyuntong.goods.service.client.transport.vo;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 我的货源列表实体
 *
 * <AUTHOR>
 * @since 2025-01-06 11:12
 */
@Data
public class MyTransportListVO {
    private Long tsId;
    /**
     * 发布人id
     */
    private Long userId;
    /**
     * 发货人昵称
     */
    private String nickName;
    /**
     * 发货人手机号
     */
    private String cellPhone;
    /**
     * 出发地
     */
    private String startPoint;
    /**
     * 目的地
     */
    private String destPoint;
    /**
     * 货物内容
     */
    private String taskContent;
    /**
     * 运单编号
     */
    private String tsOrderNo;
    /**
     * 长
     */
    private String length;
    /**
     * 宽
     */
    private String wide;
    /**
     * 高
     */
    private String high;
    /**
     * 0是不需要、1是需要
     */
    private String isInfoFee;
    /**
     * 发布日期 (毫秒)
     */
    private Date publishTime;
    /**
     * 车辆ID
     */
    private Long carId;
    /**
     * 车头牌照头字母
     */
    private String headCity;
    /**
     * 车头牌照号码
     */
    private String headNo;
    /**
     * 挂车牌照头字母
     */
    private String tailCity;
    /**
     * 挂车牌照号码
     */
    private String tailNo;
    /**
     * 承运人userid
     */
    private Long carryUserId;
    /**
     * 承运人手机号码
     */
    private String carryCellPhone;
    /**
     * 承运人姓名
     */
    private String carryName;
    /**
     * 是否更改过所查车辆，0-未更改，1-已更改
     */
    private Integer isChangeCar;
    /**
     * 是否允许定位车辆，0-允许，1-拒绝
     */
    private Integer isAllowLocation;
    /**
     * 创建时间
     */
    private Date ctime;
    /**
     * 修改时间
     */
    private Date mtime;
    /**
     * 成交价
     */
    private BigDecimal dealPrice;
    /**
     * 撤销日期 (毫秒)
     */
    private Date cancelTime;
    /**
     * 成交时间(毫秒)
     */
    private Date loadTime;
    /**
     * 货物状态， 1有效（发布中），0无效，2待定（QQ专用），3阻止（QQ专用），4成交，5撤销状态
     */
    private Integer goodStatus;
    private Long srcMsgId;
    /**
     * 重量单位吨
     */
    private String weight;
    /**
     * 2021.10.08 新增参数 货源撤销原因
     */
    private String backoutReasonKey;
    /**
     * 2018-12-25 编辑重发按钮逻辑判断
     * 信息费运单状态：0待接单 1有人支付成功 （货主的待同意 ）2装货中（车主是待装货）3车主装货完成 4系统装货完成 5异常上报
     */
    private String infoStatus;
    // 2019-01-28日新增 查看人与联系人查看数据
    /**
     * 查看次数
     */
    private Integer viewCount = 0;
    /**
     * 联系人数
     */
    private Integer contactCount = 0;
    /**
     * 2019-12-05 运费
     */
    private String price;
    /**
     * 首发货源类型（电议1，一口价2）
     */
    private Integer firstPublishType;
    /**
     * 货源类型（电议1，一口价2）
     */
    private Integer publishType;
    /**
     * 信息费（分）
     */
    private BigDecimal infoFee;
    /**
     * 技术服务费（分）
     */
    private BigDecimal tecServiceFee;
    /**
     * 加价次数
     */
    private String addMoneyNum;
    private Date releaseTime;
    /**
     * 调车数量
     */
    private Integer shuntingQuantity;

    /**
     * 开始装车时间
     */
    private Date beginLoadingTime;
    /**
     * 装车时间
     */
    private Date loadingTime;
    /**
     * 开始卸车时间
     */
    private Date beginUnloadTime;
    /**
     * 卸车时间
     */
    private Date unloadTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 货物型号
     */
    private String type;
    /**
     * 货物品牌
     */
    private String brand;
    /**
     * 订金类型（0不退还；1退还）
     */
    private Integer refundFlag;
    /**
     * 是否后台货源0不是1是
     */
    private Integer isBackendTransport = 0;
    private Integer orderCount = 0;
    /**
     * 是否显示转一口价按钮（不再使用）
     */
    private Integer priceButtonShow = 1;
    /**
     * 刷新次数
     */
    private Integer resendCounts;
    // 出发地 目的地 省市区
    private String startProvinc;
    private String startCity;
    private String startArea;
    private String destProvinc;
    private String destCity;
    private String destArea;
    private String startDetailAdd;
    private String destDetailAdd;
    private BigDecimal startLongitude;
    private BigDecimal startLatitude;
    private BigDecimal destLongitude;
    private BigDecimal destLatitude;
    /**
     * 是否是保障货源 0：否  1：是
     */
    private Integer guaranteeGoods;
    /**
     * 是否有信用曝光权限（0没有；1有（未点击）；2有（已点击）；）
     */
    private Integer creditRetop;
    /**
     * 是否有信用曝光权限（0没有；1有（未点击）；2有（已点击）；）
     */
    private Integer userLevel;
    /**
     * json格式的标签字符串（参考plat内TransportLabelJson类）
     */
    private String labelJson;
    /**
     * 货源是否可以刷新 0 可以 1不可以
     */
    private Integer showGoodsRefresh = 1;
    /**
     * 优推好车主过期时间
     */
    private Date priorityRecommendExpireTime;
    /**
     * 是否是优车货源（0:否 1：是）
     */
    private Integer excellentGoods;
    /**
     * 是否优车2.0货源：1-否，2-是
     */
    private Integer excellentGoodsTwo;
    /**
     * 货源来源（1货主；2调度客服；3:个人货主  4：运满满货源）
     */
    private Integer sourceType;
    /**
     * 标准货名备注
     */
    private String machineRemark;
    /**
     * 优车发货卡id
     */
    private Long excellentCardId;
    /**
     * 这个货被车方报价的总次数
     */
    private Integer transportQuotedPriceTimes;
    /**
     * 是否开票货源 0：否；1：是
     */
    private Integer invoiceTransport;
    /**
     * 附加运费
     */
    private String additionalPrice;
    /**
     * 企业税率
     */
    private BigDecimal enterpriseTaxRate;
    /**
     * 加价按钮展示样式 1:按钮；2:卡片；3:新样式卡片(低于30天内平均运费X元)
     */
    private Integer addMoneyButtonStyle;
    /**
     * 低于30天内平均运费X元，X为成交均价-当前运费
     */
    private BigDecimal priceDifference;
    private BigDecimal distance;
    /**
     * 开票货源开票主体ID
     */
    private Long invoiceSubjectId;
    /**
     * 用车类型：1-整车，2-零担
     */
    private Integer useCarType;
    /**
     * 是否允许优车2.0可电议 (1可电议，0不可电议)
     */
    private Integer isAllowTeleNegotiation = 0;
    /**
     * 开票货源指派车方联系方式
     */
    private String assignCarTel;
    /**
     * 货类
     */
    private String goodTypeName;

    /**
     * 相似货源是否有运费
     */
    private Boolean similarityTransportHavePrice = false;

    /**
     * 是否送曝光卡：1是0否
     */
    private Integer giveawayExposureCard;

    /**
     * 显示任务按钮（0：不显示；1：曝光；2：填价；3：加价；5：补齐货物信息）
     */
    private Integer showTaskBtn;

    /**
     * 显示任务文案
     */
    private ShowTaskInfo showTaskInfo;

    @Getter
    @Setter
    public static class ShowTaskInfo {
        private String title;
        private String content;
    }

    /**
     * 优惠价格
     */
    private Integer perkPrice;
}
