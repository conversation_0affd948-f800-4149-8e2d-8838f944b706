package com.teyuntong.goods.service.client.quotedprice.dto;

import lombok.Data;

/**
 * 货源报价同意实体
 *
 * <AUTHOR>
 * @since 2025-04-22 14:00
 */
@Data
public class QuotedPriceDTO {
    /**
     * 用户ID
     */
    private Long userId;

    // private String clientVersion;
    //
    // private Integer clientSign;

    /**
     * 回价助手自动同意报价: 1-车主出价自动同意，2-回价助手任务设置自动同意
     */
    private Integer autoAgree;

    /**
     * 货源ID
     */
    private Long srcMsgId;

    /**
     * 报价记录ID
     */
    private Long transportQuotedPriceId;

    private Integer price;

    private String reason;

    /**
     * 是否将运费修改为本次回价的金额: 0-否，1-是
     */
    private Integer changePrice;
}
