package com.teyuntong.goods.service.client.quotedprice.service;

import com.teyuntong.goods.service.client.quotedprice.dto.QuotedPriceDTO;
import com.teyuntong.goods.service.client.quotedprice.vo.*;
import com.teyuntong.goods.service.client.transport.vo.TransportMainVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 货源报价相关
 *
 * <AUTHOR>
 * @since 2024-11-05 15:13
 */
public interface TransportQuotedPriceRpcService {
    /**
     * 车获取某个货源自己的报价
     *
     * @param carUserId
     * @param srcMsgId
     * @return
     */
    @PostMapping("/rpc/quotedPrice/getCarToTransportQuotedPrice")
    TransportQuotedPriceCarVO getCarToTransportQuotedPrice(@RequestParam("carUserId") Long carUserId,
                                                           @RequestParam("srcMsgId") Long srcMsgId);

    /**
     * 货源详情顶部报价列表
     *
     * @param srcMsgId
     * @return
     */
    @GetMapping("/rpc/quotedPrice/getQuotedPriceListInSingleDetailPage")
    TransportQuotedPriceDataInDetailPageVO getQuotedPriceListInSingleDetailPage(@RequestParam("srcMsgId") Long srcMsgId,
                                                                                @RequestParam("userId") Long userId);

    /**
     * 车离开货源详情报价挽留弹窗
     *
     * @param srcMsgId
     * @return
     */
    @GetMapping("/rpc/quotedPrice/getCarLeaveTransportSingleDetailTabData")
    Boolean getCarLeaveTransportSingleDetailTabData(@RequestParam("srcMsgId") Long srcMsgId,
                                                    @RequestParam("userId") Long userId);

    /**
     * 获取报价货源详情
     *
     * @param srcMsgId
     * @return
     */
    @GetMapping("/rpc/quotedPrice/getTransportVO")
    TransportMainVO getTransportVO(@RequestParam("srcMsgId") Long srcMsgId,
                                   @RequestParam("userId") Long userId);


    /**
     * 货在报价列表web页面顶部氛围文案
     *
     * @param srcMsgId
     * @return
     */
    @GetMapping("/rpc/quotedPrice/getTransportQuotedPricePageWord")
    String getTransportQuotedPricePageWord(@RequestParam("srcMsgId") Long srcMsgId);

    /**
     * 货获取所有发布中货源的所有报价列表
     *
     * @param userId
     * @return
     */
    @GetMapping("/rpc/quotedPrice/getAllPublishingTransportQuotedPriceList")
    List<AllTransportQuotedPriceVO> getAllPublishingTransportQuotedPriceList(@RequestParam("userId") Long userId);

    /**
     * 获取货源最新一条是出价记录还是沟通记录
     *
     * @param userId
     * @return
     */
    @GetMapping("/rpc/quotedPrice/transportNewestRecordType")
    RecordTypeVO transportNewestRecordType(@RequestParam("userId") Long userId);


    /**
     * 货主同意报价
     *
     * @param agreeDTO
     */
    @PostMapping("/rpc/quotedPrice/transportAgree")
    QuotedPriceResultVO transportAgree(@RequestBody QuotedPriceDTO agreeDTO);

    /**
     * 货主出价/拒绝报价
     *
     * @param priceDTO
     */
    @PostMapping("/rpc/quotedPrice/transportQuotedPrice")
    QuotedPriceResultVO transportQuotedPrice(@RequestBody QuotedPriceDTO priceDTO);

    /**
     * 车主报价
     *
     * @param priceDTO
     */
    @PostMapping("/rpc/quotedPrice/carQuotedPrice")
    QuotedPriceResultVO carQuotedPrice(@RequestBody QuotedPriceDTO priceDTO);

    /**
     * 车同意报价
     *
     * @param priceDTO
     */
    @PostMapping("/rpc/quotedPrice/carAgree")
    QuotedPriceResultVO carAgree(@RequestBody QuotedPriceDTO priceDTO);

    /**
     * 货主是否有货源被车方出价
     *
     * @return
     */
    @GetMapping("/rpc/quotedPrice/getTransportHaveAnyQuotedPrice")
    Boolean getTransportHaveAnyQuotedPrice(@RequestParam("userId") Long userId);

    /**
     * 车主是否存在货方回价了但车方还没想响应的货
     *
     * @return
     */
    @GetMapping("/rpc/quotedPrice/getCarHaveNewTransportQuotedPrice")
    Boolean getCarHaveNewTransportQuotedPrice(@RequestParam("userId") Long userId);


    /**
     * 货报价挽留弹窗
     *
     * @param srcMsgId
     * @return
     */
    @GetMapping("/rpc/quotedPrice/getTransportQuotedPriceLeaveTab")
    TransportQuotedPriceLeaveTabVO getTransportQuotedPriceLeaveTab(@RequestParam("srcMsgId") Long srcMsgId);

    /**
     * 货方拒绝报价弹窗数据接口
     *
     * @param transportQuotedPriceId
     * @return
     */
    @GetMapping("/rpc/quotedPrice/getTransportQuotedPriceTabData")
    TransportQuotedPriceTabDataVO getTransportQuotedPriceTabData(@RequestParam("transportQuotedPriceId") Long transportQuotedPriceId);

    /**
     * 货获取某个货源的所有报价列表
     *
     * @param srcMsgId
     * @param userId
     * @return
     */
    @PostMapping("/rpc/quotedPrice/getTransportQuotedPriceList")
    List<TransportQuotedPriceTransportVO> getTransportQuotedPriceList(@RequestParam("srcMsgId") Long srcMsgId,
                                                                      @RequestParam("userId") Long userId);

    /**
     * 校验用户是否在出价ab测
     *
     * @param userId
     * @return
     */
    @PostMapping("/rpc/quotedPrice/checkUserIsInTransportQuotedPriceABTest")
    Boolean checkUserIsInTransportQuotedPriceABTest(@RequestParam("userId") Long userId);

    /**
     * 判断要出价货源是否有效
     *
     * @param srcMsgId
     * @return
     */
    @PostMapping("/rpc/quotedPrice/checkTransportValidity")
    Boolean checkTransportValidity(@RequestParam("srcMsgId") Long srcMsgId);

    @GetMapping("/rpc/quotedPrice/carShowQuotedPriceBox")
    CarShowQuotedPriceBoxVO carShowQuotedPriceBox(@RequestParam("srcMsgId") Long srcMsgId);

}
