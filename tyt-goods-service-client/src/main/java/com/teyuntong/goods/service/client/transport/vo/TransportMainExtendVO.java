package com.teyuntong.goods.service.client.transport.vo;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class TransportMainExtendVO {
    private Long id;

    /**
     * 货源ID
     */
    private Long srcMsgId;

    /**
     * 用车类型：1-整车，2-零担
     */
    private Integer useCarType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 好货模型分数
     */
    private BigDecimal goodModelScore;

    /**
     * 好货运价模型分数
     */
    private BigDecimal commissionScore;

    /**
     * 秒杀货源：1是0否
     */
    private Integer seckillGoods;

    /**
     * 运费补贴金额
     */
    private Integer perkPrice;

    /**
     * 好货标签 11:好1，12:好2，13:好3，21:中1，22:中2，23:中3，31:差1，32:差2，0:不符合
     */
    private Integer goodTransportLabel;
}