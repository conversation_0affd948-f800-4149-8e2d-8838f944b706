package com.teyuntong.goods.service.client.invoice.vo;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 开票运输配置日志VO
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Getter
@Setter
public class InvoiceTransportConfigLogVO {

    private Long id;

    /**
     * 最大吨位
     */
    private Double maxTonnage;

    /**
     * 最大吨位是否修改 0：未修改；1：修改了
     */
    private Integer maxTonnageIsChange;

    /**
     * 最大长度
     */
    private Double maxLength;

    /**
     * 最大长度是否修改 0：未修改；1：修改了
     */
    private Integer maxLengthIsChange;

    /**
     * 最大宽度
     */
    private Double maxWidth;

    /**
     * 最大宽度 0：未修改；1：修改了
     */
    private Integer maxWidthIsChange;

    /**
     * 最大高度
     */
    private Double maxHeight;

    /**
     * 最大高度是否修改 0：未修改；1：修改了
     */
    private Integer maxHeightIsChange;

    /**
     * 最大运费金额总数
     */
    private Double maxTotalTransportPrice;

    /**
     * 最大运费金额总数是否修改 0：未修改；1：修改了
     */
    private Integer maxTotalTransportPriceIsChange;

    /**
     * 配置修改时间
     */
    private Date createTime;

    /**
     * 配置修改人ID
     */
    private Long createUserId;

    /**
     * 配置修改人用户名
     */
    private String createUserName;

    /**
     * 运费总价上限
     */
    private String maxPrice;

    /**
     * 超限证 装货阶段是否必传 0否 1是
     */
    private Integer loadNeedLoadStage;

    /**
     * 超限证 卸货阶段是否必传 0否 1是
     */
    private Integer unloadNeedLoadStage;

    /**
     * 超限证 收货阶段是否必传 0否 1是
     */
    private Integer getNeedLoadStage;

    /**
     * 车头整备质量+挂车整备质量+货物重量>【】吨
     */
    private BigDecimal carWeight;

    /**
     * 车货宽>【】米
     */
    private BigDecimal carWidth;

    /**
     * 车货高>【】米
     */
    private BigDecimal carHeight;

    /**
     * 车货长>【】米
     */
    private BigDecimal carLength;

    /**
     * 装货图片 装货阶段是否必传 0否 1是
     */
    private Integer loadNeedLoadPhoto;

    /**
     * 装货图片 收货阶段是否必传 0否 1是
     */
    private Integer getNeedLoadPhoto;

    /**
     * 卸货图片 卸货阶段是否必传 0否 1是
     */
    private Integer unloadNeedUnloadPhoto;

    /**
     * 卸货图片 收货阶段是否必传 0否 1是
     */
    private Integer getNeedUnloadPhoto;

    /**
     * 回单图片 卸货阶段是否必传 0否 1是
     */
    private Integer unlodNeedReceiptPhoto;

    /**
     * 回单图片 收货阶段是否必传 0否 1是
     */
    private Integer getNeedReceiptPhoto;

    /**
     * 是否修改了公里价格限制 0否 1是
     */
    private Integer updatePriceConfig;

    /**
     * 接单车辆要求配置 适用类型 1三方开票
     */
    private Integer takeOrderCarRequireType;

    /**
     * 接单车辆要求配置 三方要求-证件缺失 且认证状态-认证通过 0否 1是
     */
    private Integer carRequireAuth;

    /**
     * 接单车辆要求配置 三方要求-证件待审 0否 1是
     */
    private Integer carRequireWaitAuth;

    /**
     * 接单车辆要求配置 三方要求-符合要求 0否 1是
     */
    private Integer carRequireConform;

    /**
     * 接单车辆要求配置 三方要求-不符合要求 0否 1是
     */
    private Integer carRequireNoConform;

    /**
     * 是否可指派车方 0：否；1：是
     */
    private Integer canAssignCar;

    /**
     * 是否支持分段支付 0：不支持；1：支持
     */
    private Integer segmentedPayments;

    /**
     * 价格配置
     */
    private List<InvoiceTransportPriceConfigVO> tytInvoiceTransportPriceConfigList;
}
